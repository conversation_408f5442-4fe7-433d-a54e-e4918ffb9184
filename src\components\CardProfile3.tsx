/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-15 18:29:57
 * @LastEditTime: 2024-12-17 16:16:10
 * @LastEditors: <PERSON><PERSON>
 * @Description: 合作机构卡片
 */

interface IProps {
  url: string;
  title: string;
  content: string;
  className?: string;
  style?: React.CSSProperties;
  classNames?: {
    titleContainer?: string;
    imageContainer?: string;
    contentContainer?: string;
    title?: string;
    content?: string;
  };
  onClick?: () => void;
}

export const CardProfile3 = (props: IProps) => {
  const { onClick, url, title, content, className, style, classNames } = props;

  const styles = {
    transition: 'transition-colors ease-in-out duration-400',
    contentContainer:
      'rounded-bl-[.08rem] rounded-br-[.08rem] bg-white w-full h-[1.8rem] flex flex-col items-center p-[.3rem] gap-[.1rem] group-hover:bg-[#2B65F6]',
    title: 'text-[.2rem] font-semibold text-[#222222] group-hover:text-white',
    content: 'text-[.16rem] font-normal text-[#555555] group-hover:text-white',
  };

  return (
    <div
      onClick={onClick}
      className={`group cursor-pointer flex flex-col w-[2.6rem] h-[3.40rem] bg-[#F3F8FF] items-center ${className}`}
      style={style}
    >
      <div
        className={`rounded-tl-[.08rem] rounded-tr-[.08rem] w-full h-[1.6rem] ${classNames?.imageContainer}`}
      >
        <img src={url} className="rounded-tl-[.08rem] rounded-tr-[.08rem] w-full h-full" />
      </div>
      <div
        className={`${styles.contentContainer} ${styles.transition}  ${classNames?.contentContainer}`}
      >
        <p className={`${styles.title} ${classNames?.title} ${styles.transition}`}>{title}</p>
        <p className={`${styles.content} ${classNames?.content} ${styles.transition}`}>{content}</p>
      </div>
    </div>
  );
};
