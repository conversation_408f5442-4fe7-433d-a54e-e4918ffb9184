/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-14 00:08:16
 * @LastEditTime: 2025-01-06 16:54:45
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import imgAiaas1 from '@/assets/aiaas/img_aiaas_1.jpg';
import imgAiaas2 from '@/assets/aiaas/img_aiaas_2.jpg';
import imgAiaas3 from '@/assets/aiaas/img_aiaas_3.jpg';
import { SectionTitle } from '@/components/SectionTips';
import { Button } from 'antd';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-14 00:08:16
 * @LastEditTime: 2024-12-16 01:03:09
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description:
 */
export default function Platform() {
  return (
    <div className="flex flex-col w-full">
      <header
        className="h-[7.88rem] flex flex-col justify-center px-[2rem]"
        style={{
          backgroundImage: `url(${imgAiaas1})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
        }}
      >
        <div>
          <p className="font-medium text-[.64rem]">
            <span>
              专为<span className="text-[#0067FF]">病媒数字</span>监测管理而生
            </span>
          </p>
          <p className="font-normal text-[.16rem] text-[#222222]">
            星尘AS平台是一个基于云的AI系统，具备图像识别、监控、预测和统计功能。
          </p>
        </div>
        <Button className="bg-[#0067FF] text-white w-[1.54rem] h-[.6rem] border-0 mt-[.6rem]">
          <span className="font-medium text-[.2rem]">立即试用</span>
        </Button>
      </header>
      <section
        className="h-[11.5rem] flex flex-col items-center"
        style={{
          backgroundImage: `url(${imgAiaas3})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
        }}
      >
        <SectionTitle
          className="mt-[1.01rem]"
          title="Aiaas平台"
          content="平台依托强大硬件和KBS云平台，深度集成图像识别、在线巡检等核心功能，采用先进技术实现高效图像分析和AI预警，能精准统计蚊鼠信息并监测环境数据，同时支持训练微调、知识库更新和丰富API接口，确保高效监测和灵活业务"
        />
        <img src={imgAiaas2} className="w-[15.2rem] h-[6.68rem] mt-[.5rem]" />
      </section>
    </div>
  );
}
