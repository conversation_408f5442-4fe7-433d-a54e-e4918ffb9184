import { ReactComponent as Avatar } from '@/assets/about/Act/avatar.svg';
import HotTopic from './HotTopic/HotTopic';
import list from './content';

export default function Act() {
  return (
    <div className="bg-[#f6f5fa] pb-[1rem] min-h-[800px]">
      <div className="pt-[1.7rem]  flex w-[13rem] mx-auto">
        <div className="w-[8.64rem]">
          {list.map((item, index) => {
            return (
              <a
                href={`/about/act/${item.id}`}
                className="mb-[.4rem] flex h-[1.8rem] cursor-pointer"
                key={index}
              >
                <img
                  src={item.img}
                  alt=""
                  className="w-[3.4rem] h-[1.8rem] shrink-0 rounded-[.12rem]"
                />
                <div className="ml-[.3rem] py-[.12rem]">
                  <div className="text-[.22rem] leading-[.31rem] h-[.62rem] text-[#333] text-row-two">
                    {item.title}
                  </div>
                  <div className="text-[.14rem] leading-[.20rem] text-[#666] mt-[.1rem] text-row-two">
                    {item.abstract}
                  </div>
                  <div className="text-[.16rem] leading-[.24rem] h-[.24rem]  flex items-center mt-[.2rem]">
                    <Avatar className="shrink-0 text-[#5188FF]"></Avatar>
                    <div className="text-[#333] ml-[.08rem]">{item.author}</div>
                    <div className="text-[#999] mx-[.1rem]">|</div>
                    <div className="text-[#999]">{item.date}</div>
                  </div>
                </div>
              </a>
            );
          })}
        </div>
        <HotTopic></HotTopic>
      </div>
    </div>
  );
}
