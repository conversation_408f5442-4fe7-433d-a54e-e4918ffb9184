/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 10:57:17
 * @LastEditTime: 2024-12-16 10:59:17
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}
export const IconBarChart = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="30"
      height="30"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 16.25V21.25M20 13.75V21.25M15 8.75V21.25M9.75 26.25H20.25C22.3502 26.25 23.4003 26.25 24.2025 25.8413C24.9081 25.4817 25.4817 24.9081 25.8413 24.2025C26.25 23.4003 26.25 22.3502 26.25 20.25V9.75C26.25 7.6498 26.25 6.5997 25.8413 5.79754C25.4817 5.09193 24.9081 4.51825 24.2025 4.15873C23.4003 3.75 22.3502 3.75 20.25 3.75H9.75C7.6498 3.75 6.5997 3.75 5.79754 4.15873C5.09193 4.51825 4.51825 5.09193 4.15873 5.79754C3.75 6.5997 3.75 7.6498 3.75 9.75V20.25C3.75 22.3502 3.75 23.4003 4.15873 24.2025C4.51825 24.9081 5.09193 25.4817 5.79754 25.8413C6.5997 26.25 7.6498 26.25 9.75 26.25Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
