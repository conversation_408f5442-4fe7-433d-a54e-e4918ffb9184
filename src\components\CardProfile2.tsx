/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-15 18:29:57
 * @LastEditTime: 2024-12-16 17:18:47
 * @LastEditors: <PERSON><PERSON>
 * @Description: 合作机构卡片
 */

import { ReactNode } from 'react';

interface IProps {
  icon: ReactNode;
  title: string;
  content: string;
  className?: string;
  titleClassName?: string;
  contentClassName?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

export const CardProfile2 = (props: IProps) => {
  const { onClick, icon, title, content, className, titleClassName, contentClassName, style } =
    props;
  return (
    <div
      onClick={onClick}
      className={`rounded-[0.08rem] flex flex-col w-[4.80rem] h-[2.44rem] bg-[#F3F8FF] p-[.3rem] ${className}`}
      style={style}
    >
      <div className="h-[.30rem]">{icon}</div>
      <p
        className={`text-[.2rem] font-semibold text-[#222222] mt-[.32rem] mb-[.2rem] ${titleClassName}`}
      >
        {title}
      </p>
      <p className={`text-[.16rem] font-normal text-[#555555] ${contentClassName}`}>{content}</p>
    </div>
  );
};
