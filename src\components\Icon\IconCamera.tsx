/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 11:01:19
 * @LastEditTime: 2024-12-17 16:38:43
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}
export const IconCamera = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="30"
      height="30"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.5 10.4715C2.5 10.0336 2.5 9.81468 2.51827 9.63026C2.6945 7.85159 4.10159 6.4445 5.88026 6.26827C6.06468 6.25 6.29545 6.25 6.75698 6.25C6.93482 6.25 7.02374 6.25 7.09923 6.24543C8.06326 6.18704 8.90744 5.5786 9.26768 4.6825C9.29589 4.61232 9.32226 4.53321 9.375 4.375C9.42774 4.21679 9.45411 4.13768 9.48232 4.0675C9.84256 3.1714 10.6867 2.56296 11.6508 2.50457C11.7263 2.5 11.8097 2.5 11.9764 2.5H18.0236C18.1903 2.5 18.2737 2.5 18.3492 2.50457C19.3133 2.56296 20.1574 3.1714 20.5177 4.0675C20.5459 4.13768 20.5723 4.21679 20.625 4.375C20.6777 4.53321 20.7041 4.61232 20.7323 4.6825C21.0926 5.5786 21.9367 6.18704 22.9008 6.24543C22.9763 6.25 23.0652 6.25 23.243 6.25C23.7046 6.25 23.9353 6.25 24.1197 6.26827C25.8984 6.4445 27.3055 7.85159 27.4817 9.63026C27.5 9.81468 27.5 10.0336 27.5 10.4715V20.25C27.5 22.3502 27.5 23.4003 27.0913 24.2025C26.7317 24.9081 26.1581 25.4817 25.4525 25.8413C24.6503 26.25 23.6002 26.25 21.5 26.25H8.5C6.3998 26.25 5.3497 26.25 4.54754 25.8413C3.84193 25.4817 3.26825 24.9081 2.90873 24.2025C2.5 23.4003 2.5 22.3502 2.5 20.25V10.4715Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15 20.625C17.7614 20.625 20 18.3864 20 15.625C20 12.8636 17.7614 10.625 15 10.625C12.2386 10.625 10 12.8636 10 15.625C10 18.3864 12.2386 20.625 15 20.625Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
