/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 12:13:33
 * @LastEditTime: 2024-12-18 15:59:02
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}

export const IconMessage = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.33333 6.66853C8.48016 6.25113 8.76998 5.89917 9.15144 5.67498C9.53291 5.45079 9.98141 5.36884 10.4175 5.44364C10.8536 5.51844 11.2492 5.74517 11.5341 6.08367C11.8191 6.42217 11.975 6.8506 11.9744 7.29306C11.9744 8.54213 10.1008 9.16667 10.1008 9.16667M10.1249 11.6667H10.1333M5.83333 15V16.9463C5.83333 17.3903 5.83333 17.6123 5.92436 17.7263C6.00352 17.8255 6.12356 17.8832 6.25045 17.8831C6.39636 17.8829 6.56973 17.7442 6.91646 17.4668L8.90434 15.8765C9.31043 15.5517 9.51347 15.3892 9.73957 15.2737C9.94017 15.1712 10.1537 15.0963 10.3743 15.051C10.6231 15 10.8831 15 11.4031 15H13.5C14.9001 15 15.6002 15 16.135 14.7275C16.6054 14.4878 16.9878 14.1054 17.2275 13.635C17.5 13.1002 17.5 12.4001 17.5 11V6.5C17.5 5.09987 17.5 4.3998 17.2275 3.86502C16.9878 3.39462 16.6054 3.01217 16.135 2.77248C15.6002 2.5 14.9001 2.5 13.5 2.5H6.5C5.09987 2.5 4.3998 2.5 3.86502 2.77248C3.39462 3.01217 3.01217 3.39462 2.77248 3.86502C2.5 4.3998 2.5 5.09987 2.5 6.5V11.6667C2.5 12.4416 2.5 12.8291 2.58519 13.147C2.81635 14.0098 3.49022 14.6836 4.35295 14.9148C4.67087 15 5.05836 15 5.83333 15Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
