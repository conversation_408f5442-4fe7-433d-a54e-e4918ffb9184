/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-12 20:11:48
 * @LastEditTime: 2024-12-18 16:25:06
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}
export const IconPhoto2 = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.08301 4.16671C2.08301 3.70647 2.4561 3.33337 2.91634 3.33337H17.083C17.5433 3.33337 17.9163 3.70647 17.9163 4.16671V15.8334C17.9163 16.2936 17.5433 16.6667 17.083 16.6667H2.91634C2.4561 16.6667 2.08301 16.2936 2.08301 15.8334V4.16671Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.04199 7.5C6.38716 7.5 6.66699 7.22017 6.66699 6.875C6.66699 6.52983 6.38716 6.25 6.04199 6.25C5.69683 6.25 5.41699 6.52983 5.41699 6.875C5.41699 7.22017 5.69683 7.5 6.04199 7.5Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.24967 10L8.33301 11.6667L10.833 8.75L17.9163 14.1667V15.8333C17.9163 16.2936 17.5433 16.6667 17.083 16.6667H2.91634C2.4561 16.6667 2.08301 16.2936 2.08301 15.8333V14.1667L6.24967 10Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
    </svg>
  );
};
