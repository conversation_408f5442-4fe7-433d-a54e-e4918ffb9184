<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用场景 - 银河星尘</title>
    <link rel="stylesheet" href="styles.css">
    <link href="css/all.min.css" rel="stylesheet">
    <script src="js/gsap.min.js"></script>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="company_logo.png" alt="银河星尘" class="logo-image">
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">首页</a>
                </li>
                <li class="nav-item">
                    <a href="products.html" class="nav-link">产品中心</a>
                </li>
                <li class="nav-item dropdown">
                    <a href="scenarios.html" class="nav-link active">应用场景</a>
                    <div class="dropdown-menu">
                        <a href="scenarios.html" class="dropdown-item">工地监测</a>
                        <a href="scenarios.html" class="dropdown-item">公园管理</a>
                        <a href="scenarios.html" class="dropdown-item">垃圾场治理</a>
                        <a href="scenarios.html" class="dropdown-item">学校防护</a>
                        <a href="scenarios.html" class="dropdown-item">医院环境</a>
                        <a href="scenarios.html" class="dropdown-item">园区服务</a>
                        <a href="scenarios.html" class="dropdown-item">贸易市场</a>
                        <a href="scenarios.html#cases" class="dropdown-item">落地案例</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="news.html" class="nav-link">新闻中心</a>
                </li>
                <li class="nav-item">
                    <a href="company.html" class="nav-link">企业信息</a>
                </li>
            </ul>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <main class="scenarios-main">
        <!-- 应用场景视频区域 -->
        <section class="scenarios-video-section slide-in-scale">
            <div class="video-container">
                <video class="scenarios-video" autoplay muted loop playsinline>
                    <source src="product.mp4" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>

                <!-- 视频背景遮罩 -->
                <div class="video-overlay"></div>

                <!-- 场景标签 - 随机分布 -->
                <div class="scenario-tags">
                    <button class="scenario-tag" data-scenario="workplace" style="position: absolute; top: 15%; left: 12%;">
                        <div class="tag-content">
                            <div class="tag-circle">
                                <i class="fas fa-hard-hat"></i>
                            </div>
                            <span class="tag-text">工地监测</span>
                        </div>
                    </button>

                    <button class="scenario-tag" data-scenario="school" style="position: absolute; top: 60%; left: 22%;">
                        <div class="tag-content">
                            <div class="tag-circle">
                                <i class="fas fa-school"></i>
                            </div>
                            <span class="tag-text">学校防护</span>
                        </div>
                    </button>

                    <button class="scenario-tag" data-scenario="park" style="position: absolute; top: 25%; right: 15%;">
                        <div class="tag-content">
                            <div class="tag-circle">
                                <i class="fas fa-tree"></i>
                            </div>
                            <span class="tag-text">公园管理</span>
                        </div>
                    </button>

                    <button class="scenario-tag" data-scenario="hospital" style="position: absolute; top: 40%; left: 45%;">
                        <div class="tag-content">
                            <div class="tag-circle">
                                <i class="fas fa-hospital"></i>
                            </div>
                            <span class="tag-text">医院环境</span>
                        </div>
                    </button>

                    <button class="scenario-tag" data-scenario="waste" style="position: absolute; top: 70%; right: 30%;">
                        <div class="tag-content">
                            <div class="tag-circle">
                                <i class="fas fa-trash"></i>
                            </div>
                            <span class="tag-text">垃圾站治理</span>
                        </div>
                    </button>

                    <button class="scenario-tag" data-scenario="garden" style="position: absolute; top: 50%; right: 8%;">
                        <div class="tag-content">
                            <div class="tag-circle">
                                <i class="fas fa-building"></i>
                            </div>
                            <span class="tag-text">园区服务</span>
                        </div>
                    </button>

                    <button class="scenario-tag" data-scenario="market" style="position: absolute; top: 35%; left: 75%;">
                        <div class="tag-content">
                            <div class="tag-circle">
                                <i class="fas fa-store"></i>
                            </div>
                            <span class="tag-text">贸易市场</span>
                        </div>
                    </button>
                </div>

                <!-- 信息弹出框 -->
                <div class="scenario-info-box" id="scenarioInfoBox">
                    <div class="info-box-content">
                        <div class="info-box-header">
                            <h3 id="infoBoxTitle">场景名称</h3>
                            <button class="info-box-close" id="infoBoxClose">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="info-box-body">
                            <div class="info-section">
                                <h4>为什么要做病媒监测</h4>
                                <p id="infoBoxReason">原因说明</p>
                            </div>
                            <div class="info-section">
                                <h4>监测重点</h4>
                                <ul id="infoBoxTargets">
                                    <li>监测项目1</li>
                                    <li>监测项目2</li>
                                    <li>监测项目3</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 页面标题 -->
                <div class="scenarios-title">
                    <h1 style="color: #4A90E2;">应用场景</h1>
                    <p style="color: #4A90E2;">全方位病媒监测解决方案</p>
                </div>
            </div>
        </section>

        <!-- 落地案例区域 -->
        <section id="cases" class="cases-section">
            <!-- 案例页面头部 -->
            <div class="cases-hero slide-in-scale">
                <!-- 浮动的毛玻璃球 -->
                <div class="glass-ball-2"></div>
                <div class="glass-ball-3"></div>
                <div class="glass-ball-4"></div>
                <div class="glass-ball-5"></div>
                <div class="glass-ball-6"></div>

                <div class="container">
                    <div class="cases-header">
                        <div class="header-content">
                            <div class="header-left">
                                <h1 class="cases-title">Our Cases</h1>
                            </div>
                            <div class="header-right">
                                <div class="cases-info">
                                    <h2 class="cases-subtitle">落地案例</h2>
                                    <p class="cases-description">
                                        银河星尘凭借先进的AI智能识别技术，为医疗机构、政府部门、生态景区、建筑工地、城市公园等各类场景提供专业的病媒生物监测服务。<strong class="highlight-text">专注于病媒种类识别、密度分析、活动节律监测和预警预测，为客户提供精准的数据支撑。</strong>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 案例列表 -->
            <div class="cases-list">
                <div class="container">
                    <div class="cases-grid">
                        <!-- 案例1：医院监测项目 -->
                        <div class="case-item">
                            <div class="case-content">
                                <div class="case-header">
                                    <div class="case-title-row">
                                        <h2 class="case-title">**医院虫媒密度数字监测项目</h2>
                                        <div class="case-category">医疗机构</div>
                                    </div>
                                </div>
                                <div class="case-row">
                                    <div class="case-overview">
                                        <h3>项目概述</h3>
                                        <p>
                                            本项目对**医院及其周边区域开展系统性病媒生物监测，
                                            全面掌握主要蚊虫种类、数量、性别分布及生境特征，
                                            建立医院专属的病媒生物基础数据库，为科学防控提供数据支撑。
                                        </p>
                                    </div>

                                    <div class="case-content-grid">
                                        <div class="case-image">
                                            <div class="image-carousel" data-carousel="hospital">
                                                <div class="carousel-container">
                                                    <div class="carousel-slide active">
                                                        <img src="assets/land_case/hospital/1.jpg" alt="医院监测项目" class="project-image">
                                                    </div>
                                                    <div class="carousel-slide">
                                                        <img src="assets/land_case/hospital/2.jpg" alt="医院监测项目" class="project-image">
                                                    </div>
                                                    <div class="carousel-slide">
                                                        <img src="assets/land_case/hospital/3.jpg" alt="医院监测项目" class="project-image">
                                                    </div>
                                                </div>
                                                <div class="carousel-dots">
                                                    <span class="dot active" onclick="currentSlide('hospital', 1)"></span>
                                                    <span class="dot" onclick="currentSlide('hospital', 2)"></span>
                                                    <span class="dot" onclick="currentSlide('hospital', 3)"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="case-details">
                                            <div class="case-highlights">
                                                <h3>监测内容</h3>
                                                <ul class="highlight-list">
                                                    <li><i class="fas fa-search"></i>蚊虫种类、数量、性别智能识别</li>
                                                    <li><i class="fas fa-chart-line"></i>密度分析与蚊种构成统计</li>
                                                    <li><i class="fas fa-clock"></i>日活动节律规律追踪</li>
                                                    <li><i class="fas fa-map-marked-alt"></i>空间分布模式分析</li>
                                                    <li><i class="fas fa-bell"></i>密度变化预警预测系统</li>
                                                </ul>
                                            </div>

                                            <div class="case-results">
                                                <h3>项目成果</h3>
                                                <p>
                                                    我们针对不同生境进行了蚊虫成蚊监测，成功识别出白纹伊蚊、致倦库蚊等6种主要蚊虫种类。通过密度分析和空间分布数据，为医院制定精准的病媒防控策略提供了科学依据，
                                                    实现了病媒生物的智能化监测管理。
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 案例2：街道办监测项目 -->
                        <div class="case-item">
                            <div class="case-content">
                                <div class="case-header">
                                    <div class="case-title-row">
                                        <h2 class="case-title">街道办虫媒数字监测项目</h2>
                                        <div class="case-category">政府机构</div>
                                    </div>
                                </div>
                                <div class="case-row">
                                    <div class="case-overview">
                                        <h3>项目概述</h3>
                                        <p>
                                            本项目对街道办辖区内各类生境开展精准的病媒生物监测，
                                            系统识别主要蚊虫种类、统计数量密度、分析性别比例，
                                            建立区域专属的病媒生物数据库，为科学防控提供决策依据。
                                        </p>
                                    </div>

                                    <div class="case-content-grid">
                                        <div class="case-image">
                                            <div class="image-carousel" data-carousel="street">
                                                <div class="carousel-container">
                                                    <div class="carousel-slide active">
                                                        <img src="assets/land_case/street_office/1.jpg" alt="街道办监测项目" class="project-image">
                                                    </div>
                                                    <div class="carousel-slide">
                                                        <img src="assets/land_case/street_office/2.jpg" alt="街道办监测项目" class="project-image">
                                                    </div>
                                                    <div class="carousel-slide">
                                                        <img src="assets/land_case/street_office/3.jpg" alt="街道办监测项目" class="project-image">
                                                    </div>
                                                </div>
                                                <div class="carousel-dots">
                                                    <span class="dot active" onclick="currentSlide('street', 1)"></span>
                                                    <span class="dot" onclick="currentSlide('street', 2)"></span>
                                                    <span class="dot" onclick="currentSlide('street', 3)"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="case-details">
                                            <div class="case-highlights">
                                                <h3>监测内容</h3>
                                                <ul class="highlight-list">
                                                    <li><i class="fas fa-microscope"></i>AI智能种类识别与计数</li>
                                                    <li><i class="fas fa-chart-bar"></i>密度统计与蚊种构成分析</li>
                                                    <li><i class="fas fa-clock"></i>24小时活动节律监测</li>
                                                    <li><i class="fas fa-map-marked-alt"></i>网格化空间分布追踪</li>
                                                    <li><i class="fas fa-bell"></i>密度变化预警预测</li>
                                                </ul>
                                            </div>

                                            <div class="case-results">
                                                <h3>监测成果</h3>
                                                <p>
                                                    我们针对不同生境进行了蚊虫成蚊监测，识别出白纹伊蚊、三带喙库蚊等主要蚊种。
                                                    通过日活动节律和空间分布分析，掌握了不同区域的蚊虫密度变化规律，
                                                    为街道办制定针对性防控措施提供了精准的数据支撑。
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 案例3：**山生态监测 -->
                        <div class="case-item">
                            <div class="case-content">
                                <div class="case-header">
                                    <div class="case-title-row">
                                        <h2 class="case-title">**山虫媒数字监测项目</h2>
                                        <div class="case-category">生态环境</div>
                                    </div>
                                </div>
                                <div class="case-row">
                                    <div class="case-overview">
                                        <h3>项目概述</h3>
                                        <p>
                                            本项目针对**山山地生态环境开展病媒生物智能监测，
                                            系统识别山林、水体、步道等不同生境中的蚊虫种类、数量、性别分布，
                                            建立生态旅游区专属的病媒监测数据库。
                                        </p>
                                    </div>

                                    <div class="case-content-grid">
                                        <div class="case-image">
                                            <div class="image-carousel" data-carousel="mountain">
                                                <div class="carousel-container">
                                                    <div class="carousel-slide active">
                                                        <img src="assets/land_case/tiezai_mountain/1.jpg" alt="**山监测项目" class="project-image">
                                                    </div>
                                                    <div class="carousel-slide">
                                                        <img src="assets/land_case/tiezai_mountain/2.jpg" alt="**山监测项目" class="project-image">
                                                    </div>
                                                    <div class="carousel-slide">
                                                        <img src="assets/land_case/tiezai_mountain/3.jpg" alt="**山监测项目" class="project-image">
                                                    </div>
                                                </div>
                                                <div class="carousel-dots">
                                                    <span class="dot active" onclick="currentSlide('mountain', 1)"></span>
                                                    <span class="dot" onclick="currentSlide('mountain', 2)"></span>
                                                    <span class="dot" onclick="currentSlide('mountain', 3)"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="case-details">
                                            <div class="case-highlights">
                                                <h3>监测维度</h3>
                                                <ul class="highlight-list">
                                                    <li><i class="fas fa-search"></i>多生境蚊虫种类识别</li>
                                                    <li><i class="fas fa-chart-line"></i>密度分布与构成分析</li>
                                                    <li><i class="fas fa-clock"></i>日夜活动节律监测</li>
                                                    <li><i class="fas fa-map-marked-alt"></i>空间分布热力图绘制</li>
                                                    <li><i class="fas fa-calendar-alt"></i>季节变化趋势预测</li>
                                                </ul>
                                            </div>

                                            <div class="case-results">
                                                <h3>监测发现</h3>
                                                <p>
                                                    项目识别出白纹伊蚊、骚扰阿蚊等5种主要蚊虫种类，掌握了不同海拔和生境条件下的蚊虫密度分布规律。
                                                    通过活动节律分析，发现山区蚊虫具有明显的昼夜活动差异，
                                                    为生态旅游区的游客安全和环境管理提供了科学的监测数据。
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 案例4：建筑工地监测 -->
                        <div class="case-item">
                            <div class="case-content">
                                <div class="case-header">
                                    <div class="case-title-row">
                                        <h2 class="case-title">中建局工地蚊媒监测项目</h2>
                                        <div class="case-category">建筑工程</div>
                                    </div>
                                </div>
                                <div class="case-row">
                                    <div class="case-overview">
                                        <h3>项目概述</h3>
                                        <p>
                                            本项目针对建筑工地复杂环境开展病媒生物监测，
                                            系统识别施工区、生活区、材料区等不同区域的蚊虫种类、数量及性别构成，
                                            建立工地专属的病媒监测体系，保障施工人员健康安全。
                                        </p>
                                    </div>

                                    <div class="case-content-grid">
                                        <div class="case-image">
                                            <div class="image-carousel" data-carousel="construction">
                                                <div class="carousel-container">
                                                    <div class="carousel-slide active">
                                                        <img src="assets/land_case/building_site/1.jpg" alt="工地监测项目" class="project-image">
                                                    </div>
                                                    <div class="carousel-slide">
                                                        <img src="assets/land_case/building_site/2.jpg" alt="工地监测项目" class="project-image">
                                                    </div>
                                                    <div class="carousel-slide">
                                                        <img src="assets/land_case/building_site/3.jpg" alt="工地监测项目" class="project-image">
                                                    </div>
                                                </div>
                                                <div class="carousel-dots">
                                                    <span class="dot active" onclick="currentSlide('construction', 1)"></span>
                                                    <span class="dot" onclick="currentSlide('construction', 2)"></span>
                                                    <span class="dot" onclick="currentSlide('construction', 3)"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="case-details">
                                            <div class="case-highlights">
                                                <h3>监测内容</h3>
                                                <ul class="highlight-list">
                                                    <li><i class="fas fa-search"></i>多区域蚊虫种类识别</li>
                                                    <li><i class="fas fa-chart-bar"></i>密度统计与构成分析</li>
                                                    <li><i class="fas fa-clock"></i>施工期活动节律追踪</li>
                                                    <li><i class="fas fa-map-marked-alt"></i>工地空间分布监测</li>
                                                    <li><i class="fas fa-bell"></i>密度异常预警系统</li>
                                                </ul>
                                            </div>

                                            <div class="case-results">
                                                <h3>监测成果</h3>
                                                <p>
                                                    项目识别出白纹伊蚊、致倦库蚊等4种主要蚊虫种类，掌握了不同施工阶段的蚊虫密度变化规律。
                                                    通过空间分布分析，发现临时积水区域蚊虫密度显著较高，
                                                    为工地环境管理和蚊虫防控提供了精准的数据指导。
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 案例5：城市公园监测 -->
                        <div class="case-item">
                            <div class="case-content">
                                <div class="case-header">
                                    <div class="case-title-row">
                                        <h2 class="case-title">城市公园蚊虫生态监测项目</h2>
                                        <div class="case-category">城市公园</div>
                                    </div>
                                </div>
                                <div class="case-row">
                                    <div class="case-overview">
                                        <h3>项目概述</h3>
                                        <p>
                                            本项目对城市公园绿地生态系统开展病媒生物智能监测，
                                            系统识别水体景观、绿化带、游客活动区等不同生境的蚊虫种类、数量、性别分布，
                                            建立公园专属的病媒监测数据体系。
                                        </p>
                                    </div>

                                    <div class="case-content-grid">
                                        <div class="case-image">
                                            <div class="image-carousel" data-carousel="park">
                                                <div class="carousel-container">
                                                    <div class="carousel-slide active">
                                                        <img src="assets/land_case/park/1.jpg" alt="公园监测项目" class="project-image">
                                                    </div>
                                                    <div class="carousel-slide">
                                                        <img src="assets/land_case/park/2.jpg" alt="公园监测项目" class="project-image">
                                                    </div>
                                                    <div class="carousel-slide">
                                                        <img src="assets/land_case/park/3.jpg" alt="公园监测项目" class="project-image">
                                                    </div>
                                                </div>
                                                <div class="carousel-dots">
                                                    <span class="dot active" onclick="currentSlide('park', 1)"></span>
                                                    <span class="dot" onclick="currentSlide('park', 2)"></span>
                                                    <span class="dot" onclick="currentSlide('park', 3)"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="case-details">
                                            <div class="case-highlights">
                                                <h3>监测内容</h3>
                                                <ul class="highlight-list">
                                                    <li><i class="fas fa-search"></i>多生境蚊虫种类智能识别</li>
                                                    <li><i class="fas fa-chart-line"></i>密度分布与蚊种构成分析</li>
                                                    <li><i class="fas fa-clock"></i>季节性活动节律监测</li>
                                                    <li><i class="fas fa-map-marked-alt"></i>景观区域空间分布图</li>
                                                    <li><i class="fas fa-bell"></i>密度变化预警系统</li>
                                                </ul>
                                            </div>

                                            <div class="case-results">
                                                <h3>监测成果</h3>
                                                <p>
                                                    项目识别出白纹伊蚊、三带喙库蚊等7种主要蚊虫种类，发现景观水体区域蚊虫密度最高。
                                                    通过活动节律分析，掌握了公园蚊虫的季节性变化规律和日夜活动特征，
                                                    为公园管理部门制定科学的病媒防控策略提供了数据支撑。
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 案例6：垃圾站监测 -->
                        <div class="case-item">
                            <div class="case-content">
                                <div class="case-header">
                                    <div class="case-title-row">
                                        <h2 class="case-title">垃圾中转站病媒防控监测项目</h2>
                                        <div class="case-category">环卫设施</div>
                                    </div>
                                </div>
                                <div class="case-row">
                                    <div class="case-overview">
                                        <h3>项目概述</h3>
                                        <p>
                                            本项目对垃圾中转站环境开展病媒生物智能监测，
                                            系统识别垃圾收集、转运、处理各环节的蚊蝇种类、数量及性别构成，
                                            建立中转站专属的病媒监测数据库，为环境管理提供科学依据。
                                        </p>
                                    </div>

                                    <div class="case-content-grid">
                                        <div class="case-image">
                                            <div class="image-carousel" data-carousel="waste">
                                                <div class="carousel-container">
                                                    <div class="carousel-slide active">
                                                        <img src="assets/land_case/dump/1.jpg" alt="垃圾站监测项目" class="project-image">
                                                    </div>
                                                    <div class="carousel-slide">
                                                        <img src="assets/land_case/dump/2.jpg" alt="垃圾站监测项目" class="project-image">
                                                    </div>
                                                    <div class="carousel-slide">
                                                        <img src="assets/land_case/dump/3.jpg" alt="垃圾站监测项目" class="project-image">
                                                    </div>
                                                </div>
                                                <div class="carousel-dots">
                                                    <span class="dot active" onclick="currentSlide('waste', 1)"></span>
                                                    <span class="dot" onclick="currentSlide('waste', 2)"></span>
                                                    <span class="dot" onclick="currentSlide('waste', 3)"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="case-details">
                                            <div class="case-highlights">
                                                <h3>监测范围</h3>
                                                <ul class="highlight-list">
                                                    <li><i class="fas fa-search"></i>蚊蝇种类精准识别</li>
                                                    <li><i class="fas fa-chart-bar"></i>密度统计与构成分析</li>
                                                    <li><i class="fas fa-clock"></i>24小时活动节律监测</li>
                                                    <li><i class="fas fa-map-marked-alt"></i>站内空间分布追踪</li>
                                                    <li><i class="fas fa-bell"></i>密度异常预警系统</li>
                                                </ul>
                                            </div>

                                            <div class="case-results">
                                                <h3>监测成果</h3>
                                                <p>
                                                    项目识别出家蝇、果蝇、致倦库蚊等6种主要病媒种类，掌握了垃圾处理不同环节的蚊蝇密度分布规律。
                                                    通过活动节律分析，发现病媒活动高峰时段，
                                                    为中转站制定精准的环境管理和防控措施提供了有力的数据支撑。
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <div id="footer-container"></div>
    <script src="js/footer.js"></script>

    <script src="js/common.js"></script>
    <script>
        // 应用场景页面脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 页面整体滑动动画初始化
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // 添加动画类
                        entry.target.classList.add('animate-in');
                    } else {
                        // 元素离开视口时移除动画类（支持反向动画）
                        entry.target.classList.remove('animate-in');
                    }
                });
            }, {
                threshold: 0.1, // 当元素10%可见时触发
                rootMargin: '0px 0px -50px 0px' // 提前50px触发动画
            });

            // 为所有需要动画的元素添加观察
            const animatedElements = document.querySelectorAll('.page-section, .slide-in-left, .slide-in-right, .slide-in-scale');
            animatedElements.forEach(el => {
                observer.observe(el);
            });

            // 场景信息数据
            const scenarioData = {
                workplace: {
                    title: '工地监测',
                    reason: '建筑工地环境复杂，容易滋生蚊虫等病媒生物。这些害虫不仅影响工人健康和工作效率，还可能传播疾病，造成安全隐患。通过AI智能监测，能够及时发现蚊虫成蚊活动，保障工地环境卫生。',
                    targets: ['蚊虫成蚊监测', '蚊虫密度分析', '蚊虫种类识别', '工地积水区域', '废料堆积点']
                },
                school: {
                    title: '学校防护',
                    reason: '学校是学生聚集的重要场所，蚊虫等病媒生物的存在会严重威胁师生健康。蚊虫叮咬可能传播登革热、寨卡病毒等疾病。建立完善的蚊虫成蚊监测体系，确保校园环境安全。',
                    targets: ['蚊虫密度监测', '蚊虫种类识别', '食堂卫生状况', '宿舍区域检查', '运动场地清洁']
                },
                park: {
                    title: '公园管理',
                    reason: '公园绿化覆盖率高，水体较多，为病媒生物提供了理想的栖息和繁殖环境。大量市民在此休闲娱乐，病媒防控直接关系到公众健康。通过智能监测，实现精准防控，提升公园环境质量。',
                    targets: ['水体蚊虫监测', '绿植区域检查', '垃圾桶周边', '休息区域清洁', '湖泊水质监控']
                },
                hospital: {
                    title: '医院环境',
                    reason: '医院作为医疗救治场所，患者免疫力较弱，对病媒生物特别敏感。病媒传播的疾病会加重患者病情，影响治疗效果。医院必须保持最高标准的环境卫生，病媒监测是关键环节。',
                    targets: ['病房区域监测', '污水处理系统', '医疗废物存放', '食堂厨房检查', '空调通风系统']
                },
                waste: {
                    title: '垃圾站治理',
                    reason: '垃圾站是病媒生物的主要滋生地，腐烂的有机物为蚊虫提供营养源和繁殖场所。不及时控制会导致蚊虫大量繁殖，影响周边社区环境和居民健康。',
                    targets: ['蚊虫密度监测', '蚊虫种类识别', '垃圾渗漏液', '异味扩散范围', '周边土壤污染']
                },
                garden: {
                    title: '园区服务',
                    reason: '产业园区人员密集，办公环境要求较高。病媒生物的存在会影响员工工作效率和身心健康，降低园区整体形象。通过专业监测，维护良好的办公环境，提升园区竞争力。',
                    targets: ['办公楼宇检查', '餐厅食堂监测', '停车场区域', '绿化带管理', '排水系统检查']
                },
                market: {
                    title: '贸易市场',
                    reason: '贸易市场商品种类繁多，人流量大，食物残渣和包装废料容易吸引病媒生物。这些害虫不仅污染商品，还可能传播疾病给消费者。建立监测体系，保障市场卫生安全。',
                    targets: ['食品区域监测', '储存仓库检查', '排水沟清洁', '垃圾处理点', '通风换气系统']
                }
            };

            // 获取DOM元素
            const scenarioTags = document.querySelectorAll('.scenario-tag');
            const infoBox = document.getElementById('scenarioInfoBox');
            const infoBoxTitle = document.getElementById('infoBoxTitle');
            const infoBoxReason = document.getElementById('infoBoxReason');
            const infoBoxTargets = document.getElementById('infoBoxTargets');
            const infoBoxClose = document.getElementById('infoBoxClose');
            const videoContainer = document.querySelector('.video-container');

            // 场景标签从下往上滑动动画
            gsap.fromTo('.scenario-tag',
                { y: 100, opacity: 0 },
                {
                    y: 0,
                    opacity: 1,
                    duration: 0.8,
                    stagger: 0.15,
                    delay: 0.5,
                    ease: "power2.out"
                }
            );

            // 标题动画
            gsap.fromTo('.scenarios-title',
                { y: 50, opacity: 0 },
                { y: 0, opacity: 1, duration: 0.8, delay: 0.3, ease: "power2.out" }
            );

            // 场景标签点击事件
            scenarioTags.forEach(tag => {
                tag.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const scenario = tag.getAttribute('data-scenario');
                    const data = scenarioData[scenario];

                    if (data) {
                        // 更新信息框内容
                        infoBoxTitle.textContent = data.title;
                        infoBoxReason.textContent = data.reason;

                        // 更新监测重点列表
                        infoBoxTargets.innerHTML = '';
                        data.targets.forEach(target => {
                            const li = document.createElement('li');
                            li.textContent = target;
                            infoBoxTargets.appendChild(li);
                        });

                        // 计算弹窗位置 - 紧跟对应标签
                        const tagRect = tag.getBoundingClientRect();
                        const containerRect = videoContainer.getBoundingClientRect();

                        // 计算相对于视频容器的位置
                        const relativeLeft = tagRect.left - containerRect.left;
                        const relativeTop = tagRect.top - containerRect.top;

                        // 弹窗尺寸
                        const infoBoxWidth = 380;
                        const infoBoxHeight = 280;

                        // 默认显示在标签右侧
                        let boxLeft = relativeLeft + tagRect.width + 15;
                        let boxTop = relativeTop - 20;

                        // 如果右侧空间不够，显示在左侧
                        if (boxLeft + infoBoxWidth > containerRect.width - 20) {
                            boxLeft = relativeLeft - infoBoxWidth - 15;
                        }

                        // 如果左侧空间也不够，居中显示
                        if (boxLeft < 20) {
                            boxLeft = relativeLeft + tagRect.width/2 - infoBoxWidth/2;
                            boxTop = relativeTop + tagRect.height + 15;
                        }

                        // 垂直边界检查
                        if (boxTop + infoBoxHeight > containerRect.height - 20) {
                            boxTop = relativeTop - infoBoxHeight - 15;
                        }
                        if (boxTop < 20) {
                            boxTop = 20;
                        }

                        // 水平边界最终检查
                        boxLeft = Math.max(20, Math.min(boxLeft, containerRect.width - infoBoxWidth - 20));

                        // 设置弹窗位置
                        infoBox.style.left = boxLeft + 'px';
                        infoBox.style.top = boxTop + 'px';
                        infoBox.style.right = 'auto';
                        infoBox.style.transform = 'none';

                        // 显示信息框
                        gsap.to(infoBox, {
                            opacity: 1,
                            scale: 1,
                            duration: 0.3,
                            ease: "power2.out",
                            onStart: () => {
                                infoBox.style.display = 'flex';
                            }
                        });
                    }
                });

                // 场景标签悬停效果通过CSS处理，这里可以添加额外的动画
                tag.addEventListener('mouseenter', () => {
                    // CSS已处理悬停效果，可在此添加额外动画
                });

                tag.addEventListener('mouseleave', () => {
                    // CSS已处理悬停效果，可在此添加额外动画
                });
            });

            // 关闭信息框
            function closeInfoBox() {
                gsap.to(infoBox, {
                    opacity: 0,
                    scale: 0.9,
                    duration: 0.3,
                    ease: "power2.in",
                    onComplete: () => {
                        infoBox.style.display = 'none';
                    }
                });
            }

            // 点击关闭按钮
            infoBoxClose.addEventListener('click', closeInfoBox);

            // 点击空白处关闭
            videoContainer.addEventListener('click', (e) => {
                if (e.target === videoContainer || e.target.classList.contains('scenarios-video')) {
                    closeInfoBox();
                }
            });

            // 阻止信息框内部点击冒泡
            infoBox.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        });

        // 轮播图功能
        function currentSlide(carouselName, slideIndex) {
            const carousel = document.querySelector(`[data-carousel="${carouselName}"]`);
            if (!carousel) return;

            const slides = carousel.querySelectorAll('.carousel-slide');
            const dots = carousel.querySelectorAll('.dot');

            // 隐藏所有幻灯片
            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));

            // 显示选中的幻灯片
            if (slides[slideIndex - 1]) {
                slides[slideIndex - 1].classList.add('active');
            }
            if (dots[slideIndex - 1]) {
                dots[slideIndex - 1].classList.add('active');
            }
        }
    </script>
</body>
</html>
