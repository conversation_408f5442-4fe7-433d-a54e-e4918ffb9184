/* eslint-disable @typescript-eslint/no-unused-vars */
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-15 18:29:57
 * @LastEditTime: 2024-12-17 16:09:22
 * @LastEditors: <PERSON><PERSON>
 * @Description: 合作机构卡片
 */

interface IProps {
  url: string;
  title: string;
  content: string[];
  className?: string;
  titleClassName?: string;
  contentClassName?: string;
}

export const CardProfile4 = (props: IProps) => {
  const { url, title, content, className, titleClassName, contentClassName } = props;

  const styles = {
    // 鼠标经过背景变蓝色，字体变白色
    contentContainer: ' w-full  ' + 'overflow-y-auto  ',
    transition: 'transition-colors ease-in-out duration-400',
  };

  return (
    <div
      className={`group cursor-pointer w-[4.25rem] h-[6.24rem] mx-[.2rem] ${className} rounded-[.16rem] border-[#34344A] border-[0.5px] overflow-hidden p-[.40rem] hover:bg-[#2B65F6]`}
    >
      {/* <img src={url} className="rounded-tl-[8px] rounded-tr-[8px] w-[4.8rem] h-[2.6rem]" /> */}
      <div className={`${styles.transition}  ${styles.contentContainer}`}>
        <p className={`text-[.2rem] font-semibold mb-[.1rem] text-white ${titleClassName}`}>
          {title}
        </p>
        <div
          className={`w-full text-[.16rem] font-normal  text-[#838694] ${styles.transition} group-hover:text-white`}
        >
          {content.map((item, index) => {
            return (
              <p key={index} className={`mb-0 ${contentClassName}`}>
                {item}
              </p>
            );
          })}
        </div>
      </div>
    </div>
  );
};
