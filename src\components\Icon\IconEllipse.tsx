/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 13:28:55
 * @LastEditTime: 2024-12-16 13:31:42
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

interface IProps {
  className?: string;
  color?: string;
}

export const IconEllipse = (props: IProps) => {
  const { color = '#0067FF', className } = props;
  return (
    <svg
      className={className}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="8" cy="8" r="6" stroke={color} strokeWidth="4" />
    </svg>
  );
};
