/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 01:41:43
 * @LastEditTime: 2024-12-18 16:03:25
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import imgHome1 from '@/assets/home/<USER>';
import imgHome1_1 from '@/assets/home/<USER>';
import imgHome1_2 from '@/assets/home/<USER>';
import imgHome2 from '@/assets/home/<USER>';
import imgHome3 from '@/assets/home/<USER>';
import imgHome3_1 from '@/assets/home/<USER>';
import imgInst1 from '@/assets/home/<USER>';
import imgInst2 from '@/assets/home/<USER>';
import imgInst3 from '@/assets/home/<USER>';
import imgInst4 from '@/assets/home/<USER>';
import { IconLocation } from '@/components/Icon/IconLocation';
import { IconMall } from '@/components/Icon/IconMall';
import { IconPhone } from '@/components/Icon/IconPhone';
import { history } from '@umijs/max';
import { Button, Divider } from 'antd';
import { CardProfile } from '../../components/CardProfile';

export default function Home() {
  const inst = [
    {
      key: '1',
      url: imgInst1,
      title: '中国疾病预防控制中心传染病预防控制所',
      content: '是中国疾病预防控制中心领导下的国家级细菌性传染病预防控制专业机构。',
    },
    {
      key: '2',
      url: imgInst1,
      title: '慢性非传染性疾病预防控制中心',
      content: '是中国疾控中心下属的国家级慢性病与伤害预防控制专业机构。',
    },
    {
      key: '3',
      url: imgInst2,
      title: '国家健康医疗大数据研究院(深圳)',
      content:
        '围绕大数据基础理论与算法、大数据通用软件与技术、大数据驱动的智能应用技术三大方向开展研究。',
    },
    {
      key: '4',
      url: imgInst3,
      title: '香港中文大学(深圳)',
      content: '培养具国际视野、中华传统和社会责任感的创新人才，教学科研水平高，国际合作广泛。',
    },
    {
      key: '5',
      url: imgInst4,
      title: '哈尔滨工业大学(深圳)',
      content:
        '哈工大深圳校区建于2017年，是深圳首个985校区，有11院4研究院，近700教师，1.1万学生，实力强。',
    },
  ];

  return (
    <div className="flex flex-col">
      <header
        className="relative bg-black h-[8.7rem]"
        style={{
          backgroundImage: `url(${imgHome1_2})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
        }}
      >
        <div className="absolute left-[2rem] top-[2.47rem]">
          <div className="text-white text-[.8rem]">病媒智能监测 </div>
          <div className="flex gap-[.08rem] text-[.16rem] font-normal text-white mt-[.16rem]">
            <div
              className="px-[.2rem] h-[.38rem] rounded-[1rem] flex items-center"
              style={{
                backgroundColor: 'rgba(205, 215, 224, 0.1)',
              }}
            >
              大模型智能监测
            </div>
            <div
              className="px-[.2rem] h-[.38rem] rounded-[1rem] flex items-center"
              style={{
                backgroundColor: 'rgba(205, 215, 224, 0.1)',
              }}
            >
              智能分析预测
            </div>
            <div
              className="px-[.2rem] h-[.38rem] rounded-[1rem] flex items-center"
              style={{
                backgroundColor: 'rgba(205, 215, 224, 0.1)',
              }}
            >
              AI实时预警
            </div>
          </div>
          <Button
            type="primary"
            className="w-[1.54rem] h-[.54rem] rounded-[.12rem]"
            onClick={() => history.push('/product/xxc')}
          >
            <span className="text-[.2rem] font-medium">立即体验</span>
          </Button>
        </div>
        <div className="absolute flex left-0 right-0 bottom-0 h-[1.34rem] bg-[#2B65F6] items-center">
          <div className="flex flex-col text-white  h-[1.34rem] ml-[2rem] text-center ">
            <div className="text-[.64rem] font-bold">6</div>
            <div className="text-[.16rem] font-normal">监测种类</div>
          </div>
          <Divider
            type="vertical"
            style={{ borderColor: '#ffffff' }}
            className="m-[.45rem] h-[.8rem]"
          />
          <div className="flex flex-col text-white  h-[1.34rem] text-center ">
            <div className="text-[.64rem] font-bold">32,837</div>
            <div className="text-[.16rem] font-normal">监测数量</div>
          </div>
          <Divider
            type="vertical"
            style={{ borderColor: '#ffffff' }}
            className="m-[.45rem] h-[.8rem]"
          />
          <div className="flex flex-col text-white  h-[1.34rem] text-center ">
            <div className="text-[.64rem] font-bold">700h</div>
            <div className="text-[.16rem] font-normal">监测时间</div>
          </div>
        </div>
        <img src={imgHome1_1} className="absolute bottom-0 right-[.4rem] w-[8.28rem] h-[7.34rem]" />
      </header>
      <section className="flex flex-col bg-white h-[9.47rem] px-[2rem] justify-center items-center">
        <p className="text-[.55rem] font-medium mt-[1.4rem] mb-[.25rem]">
          领先的病媒生物智能监测领域服务商
        </p>
        <div className="flex flex-row w-full justify-center ">
          <div className="flex w-[9.12rem] h-[5.2rem] rounded-tl-[20px] rounded-bl-[20px]">
            <img
              src={imgHome1}
              className="w-[9.12rem] h-[5.2rem] rounded-tl-[20px] rounded-bl-[20px] object-fill"
            />
          </div>
          <div className="bg-[#E1EEFF] flex flex-1 flex-col rounded-tr-[20px] rounded-br-[20px] justify-center pl-[.58rem] pr-[.80rem]">
            <div className="flex flex-col font-semibold text-[.2rem] mb-[.25rem]">
              <p>智能、创新、健康</p>
            </div>
            <div className="flex flex-col text-[.16rem] text-[#555555]">
              <p>
                深圳银河星尘科技有限公司(简称:银河星尘，英文简称:Galactic
                Stardust)是一家领先的人工智能技术高科技公司。汇聚来自人工智能、医疗健康及病媒生物三大领域的顶尖专家科研团队，与业内权威机构建立了稳固的合作关系。依托强大的研发实力，公司运用先进的人工智能技术，成功研发出一系列创新、高效的技术解决方案及智能监测设备。通过深度融合大模型、智能物联网(I0T)硬件和大数据分析技术，为客户提供更全面的病媒监测和预警服务。我们致力于通过技术创新，保护人类健康，增强公共卫生防疫能力，推动社会的可持续发展。
              </p>
            </div>
          </div>
        </div>
      </section>
      <section
        className="bg-black h-[12.38rem] flex flex-col items-center"
        style={{
          backgroundImage: `url(${imgHome3_1})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
        }}
      >
        <div className="w-[9.52rem] flex flex-col text-wrap text-center mb-[.38rem]">
          <p className="text-[.68rem] text-white font-semibold mt-[1.4rem] mb-[.14rem]">
            提供高质效的数字监测解决方案
          </p>
          <p className="text-[.18rem] text-white font-normal">
            银河星尘采用最先进的技术手段，融合AI病媒大模型及智能物联网（IoT）设备，结合地理信息系统（GIS）、深度学习及数据分析技术，构建了一套全面的病媒监测预警系统。该系统能够实时监测病媒生物的分布情况、活动规律和密度变化，为用户提供精准的预警服务。
          </p>
        </div>
        <img src={imgHome2} className="w-[13.4rem] h-[7.4458rem]" />
      </section>
      <section className="bg-white h-[7.61rem]">
        <div className="text-center mt-[1.4rem] mb-[.7rem]">
          <p className="text-[.68rem] font-semibold text-[#222222]">权威合作机构</p>
        </div>
        <div className="flex flex-row gap-[.2rem] justify-center">
          {inst.map((item) => {
            return (
              <CardProfile
                key={item.key}
                url={item.url}
                title={item.title}
                content={item.content}
              />
            );
          })}
        </div>
      </section>
      <section
        className={`flex flex-col w-full h-[5.8rem] px-[2rem] justify-center text-white`}
        style={{
          backgroundImage: `url(${imgHome3})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
        }}
      >
        <div className="flex flex-col w-[5.21rem] mb-[.5rem]">
          <p className="text-[.48rem] font-semibold mb-[.2rem] leading-[.67rem]">病媒智能监测</p>
          <p className="text-[.16rem] font-normal leading-[.32rem]">
            银河星尘借助尖端人工智能技术，致力于为中国乃至全球各地提供便捷、创新且高效的解决方案，以有效应对媒介传播疾病的挑战。通过汇聚全球力量，我们携手提升公共健康水平，致力于实现更为广泛的健康福祉。
          </p>
        </div>
        <div className="flex flex-col text-[.16rem] gap-[.15rem] ">
          <p className="flex  gap-[.14rem] items-center mb-0">
            <IconMall className="w-[.32rem] h-[.32rem]" />
            <span>深圳银河星尘科技有限公司</span>
          </p>
          <p className="flex  gap-[.14rem] items-center mb-0">
            <IconPhone className="w-[.32rem] h-[.32rem]" />
            <span>周先生 13316534019</span>
          </p>
          <p className="flex  gap-[.14rem] items-center mb-0">
            <IconLocation className="w-[.32rem] h-[.32rem]" />
            <span>深圳市宝安区汇智研发中心B座3楼318</span>
          </p>
        </div>
      </section>
    </div>
  );
}
