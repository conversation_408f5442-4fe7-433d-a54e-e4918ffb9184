/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 01:41:43
 * @LastEditTime: 2024-12-18 16:01:51
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { ReactNode } from 'react';
import { IconPolygon } from './Icon/IconPolygon';

interface IProps {
  title: string | ReactNode;
  content?: string;
  tips?: string;
  iconColor?: string;
  className?: string;
  classNames?: {
    title?: string;
    content?: string;
    tips?: string;
  };
}

export const SectionTitle = (props: IProps) => {
  const { title, content, className, classNames, tips, iconColor } = props;
  return (
    <div
      className={`flex flex-col text-[.48rem] font-medium text-white items-center gap-[.1rem] ${className}`}
    >
      <p className={classNames?.title}>{title}</p>
      <IconPolygon color={iconColor} className="animate-bounce w-[.12rem] h-[.1rem]" />
      {content && (
        <p className={`font-medium text-[.16rem] w-[9.96rem] text-center ${classNames?.content}`}>
          {content}
        </p>
      )}
      {tips && (
        <p
          className={`font-medium text-[.16rem] w-[9.96rem] mt-[.37rem] text-center ${classNames?.tips}`}
        >
          {tips}
        </p>
      )}
    </div>
  );
};
