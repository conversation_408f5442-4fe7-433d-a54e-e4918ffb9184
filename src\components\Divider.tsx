/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 17:53:46
 * @LastEditTime: 2024-12-17 10:23:16
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

interface IProps {
  className?: string;
}

export const CustomDivider = (props: IProps) => {
  const { className } = props;
  return (
    <div className={`w-[3.58rem] h-[2px] flex items-center ${className}`}>
      <div className="w-[1.02rem] h-[1px] border-[#2B65F6] border-[1px]"></div>
      <div className="flex-1  border-[#C0C0C0] border-[1px]"></div>
    </div>
  );
};
