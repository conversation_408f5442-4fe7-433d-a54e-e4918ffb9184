/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-13 15:59:18
 * @LastEditTime: 2024-12-19 15:50:59
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

import { create } from 'zustand';

// 1、定义数据
export const useStore = create((set) => ({
  bears: 0,
  increasePopulation: () => set((state) => ({ bears: state.bears + 1 })),
  removeAllBears: () => set({ bears: 0 }),
  updateBears: (newBears) => set({ bears: newBears }),
}));
