import imgAbout1 from '@/assets/about/img_about_1.webp';
import imgAbout2 from '@/assets/about/img_about_2.jpg';
import imgAboutActivity1 from '@/assets/about/img_about_activity_1.webp';
import imgAboutActivity2 from '@/assets/about/img_about_activity_2.webp';
import imgAboutJzg1 from '@/assets/about/img_about_jzg_1.webp';
import imgAboutJzg2 from '@/assets/about/img_about_jzg_2.webp';
import imgAboutJzg3 from '@/assets/about/img_about_jzg_3.webp';
import imgAboutJzg4 from '@/assets/about/img_about_jzg_4.webp';
import imgAboutJzg5 from '@/assets/about/img_about_jzg_5.webp';
import imgMember1 from '@/assets/about/img_about_member_1.jpg';
import { CardProfile3 } from '@/components/CardProfile3';
import { CardProfile4 } from '@/components/CardProfile4';
import { CustomerListItem } from '@/components/ListItem';
import { PrimaryText } from '@/components/PrimaryText';
import { SectionTitle } from '@/components/SectionTips';
import { history } from '@umijs/max';
import { Carousel } from 'antd';
import styles from './index.less';

export default function About() {
  return (
    <div className="flex flex-col">
      <header className="relative w-full h-[7.7rem]">
        <div className="bg-black/[.6] absolute top-0 right-0 bottom-0 left-0 z-[98]"></div>
        <img
          src={imgAbout1}
          className="absolute w-full h-[7.7rem] top-0 right-0 bottom-0 left-0 z-[90]"
          style={{
            transform: 'scaleX(-1)',
          }}
        />
        <div className="absolute z-[99] top-1/2 left-1/2 translate-x-[-50%] translate-y-[-50%] flex flex-col items-center">
          <div className="text-[.88rem] text-white">
            银河
            <PrimaryText content="星尘" />
          </div>
          <div className="text-white text-[.44rem]">领先的病媒生物智能监测领域服务商</div>
        </div>
      </header>
      <section className="bg-white w-full h-[10.66rem] flex flex-row items-center justify-center gap-[1.84rem] ">
        {/* list */}
        <div className="flex flex-col w-[6.8rem] gap-[.4rem] ">
          <div className="text-black text-[.56rem] font-semibold px-[.3rem]">银河星尘介绍</div>
          <div className="text-[#666666] text-[.16rem] px-[.3rem]">
            银河星尘汇聚来自病媒生物监测与防治、医疗健康两大领域的顶尖专家团队，与业内权威机构建立了稳固的合作关系，并吸纳了BAT等前沿科技公司的资深科研人才。依托强大的研发实力，公司运用先进的人工智能技术，成功研发出一系列创新、高效的技术解决方案及智能监测设备。通过深度融合人工智能(AI)大模型、物联网(loT)尖端技术与高级数据分析能力，致力于为用户提供全面、精准、高质效的病媒监测与预警服务，以科技力量助力公共卫生环境的持续改善与提升
          </div>
          <CustomerListItem title="使命" content={'为实现病媒可控而创新'} />
          <CustomerListItem
            title="愿景"
            content={[
              <p key="1">
                借助尖端人工智能技术，我们致力于为企业愿景中国乃至全球各地提供便捷、创新且高效的解决方案，以有效应对媒介传播疾病的挑战
              </p>,
              <p key="2">
                通过汇聚全球力量，我们携手提升公共健康水平，致力于实现更为广泛的健康福祉
              </p>,
            ]}
          />
        </div>
        <div className="flex w-[7.70rem] h-[7.6rem] ">
          <img src={imgAbout2} className="w-[7.70rem] h-[7.6rem]" />
        </div>
      </section>
      <section className="bg-[#EEF5FD] h-[6.98rem]">
        <SectionTitle
          className="mt-[1.02rem] mb-[.44rem]"
          classNames={{ title: 'text-black' }}
          iconColor="#0067FF"
          title="公司价值观"
        />
        <div className="flex items-center justify-center gap-[.3rem]">
          <CardProfile3
            url={imgAboutJzg1}
            title={'影响力'}
            content={'每一项行动都以高质量、可持续发展和追求更健康生活为导'}
          />
          <CardProfile3
            url={imgAboutJzg2}
            title={'可及性'}
            content={'技术当赋能而非排斥。让病媒消除人人可及，无论资源多少或专业程度'}
          />
          <CardProfile3
            url={imgAboutJzg3}
            title={'合作伙伴关系'}
            content={'与政府、社区及相关利益方携手作，共同消灭病媒传染疾病'}
          />
          <CardProfile3
            url={imgAboutJzg4}
            title={'赋能管理'}
            content={'为社区提供知识和工具，帮助他们积极参与健康管理'}
          />
          <CardProfile3
            url={imgAboutJzg5}
            title={'创新突破'}
            content={'不断追求突破性的解决方案，以实现最大的影响力'}
          />
        </div>
      </section>
      <section className="bg-[#1B1B2E] h-[11.69rem]">
        <SectionTitle
          className="mt-[.98rem] mb-[.44rem]"
          classNames={{ title: 'text-white' }}
          iconColor="#ffffff"
          title="核心科研成员"
        />
        <div className="w-full h-[8.17rem]">
          <Carousel
            autoplay
            slidesToShow={4}
            adaptiveHeight
            className="h-[8.17rem]"
            variableWidth
            dots={{ className: styles['slick-dots'] }}
          >
            <CardProfile4
              url={imgMember1}
              title={'李锦兴'}
              content={[
                '香港理工大学博士学位，哈尔滨工业大学（深圳）计算机科学与技术学院青年拔尖副教授、博士生导师。研究方向主要包括计算机视觉、模式识别等，在TIP、TNNLS、Inf. Fus.、CVPR、NeurIPS、ACM MM、AAAI等国际著名期刊和会议发表50余篇学术论文，在中医四诊融合方面有着多年的研究经验，主持了“多模态体外诊察信息智能感知及连续健康计算研究”、“复杂环境下的多模态无创体表信息融合及诊断研究”、“基于三部九侯的多通道脉象融合诊断方法研究”等国家级、市级科研项目，开发了中医四诊融合系统等。',
              ]}
            />
            <CardProfile4
              url={imgMember1}
              title={'刘起勇'}
              content={[
                '博士，研究员，博士生导师，中国疾病预防控制中心病媒控制首席专家，领导中国健康方面的国家研究。领导世界卫生组织（WHO）病媒监测与管理合作中心。世界卫生组织西太平洋地区办事处（WPRO）、世卫组织病媒控制顾问、世卫组织西太平洋区域办事处传染病监测和反应医务官员（STP）、世卫组织全球病媒控制常设委员会（GVCS）成员、创新病媒控制联盟（IVCC）理事，世界卫生组织病媒生物安全中心主任，传染病溯源预警与智能决策全国重点实验室PI，成立了国际登革热病媒管理联盟（IFSVM），可持续病媒管理国际论坛（IFSVM）执行主席，全国公共卫生与预防医学名词审定委员会媒介生物控制学分委会主任，中华预防医学会媒介生物学及控制分会主任委员，入选全球前10万名顶尖科学家，有超过35年公共卫生和研究经验。',
              ]}
            />
            <CardProfile4
              url={imgMember1}
              title={'于广军'}
              content={[
                '博士、研究员，博士生导师。香港中文大学（深圳）教授、博士生导师香港中文大学（深圳）医学院副院长，国家健康医疗大数据研究院（深圳）院长，香港中文大学（深圳）医学院附属第二医院院长，上海交大中国医院发展研究院医疗信息研究所所长，上海儿童精准医学大数据工程技术研究中心主任，中国医院协会信息专委会副主委，上海医学会互联网医疗分会主委。国家百千万人才工程计划，人社部有突出贡献专家。上海卫生系统优秀学科带头人、领军人才、优秀学术带头人、政府儿童工作白玉兰奖等。 ',
              ]}
            />
            {/* <CardProfile4
              url={imgMember1}
              title={'张大鹏'}
              content={[
                '--香港中文大学(深圳)数据科学学院校长讲座教授 --美国电气和电子工程师学会(IEEE)终身会士',
                '--国际模式识别协会(IAPR)和亚洲人工智能学会会士',
                '--分别于2020年和2021年当选加拿大皇家科学院和加拿大工程院两院院士',
                '--40多年来一直从事模式识别，图像处理以及生物特征识别研究，是掌纹识别、中医四诊量化及人脸美学等研究领域的开创者和领军人',
              ]}
            /> */}
            <CardProfile4
              url={imgMember1}
              title={'陈石磊'}
              content={[
                '中青林集团AI科学家。香港理工大学硕士、工信部认证高级人工智能、大模型应用专家。曾任百度集团研究院高级研究员、资深AI架构师、湖南广电集团芒果TV大数据项目总监。在相关领域发表7篇论文，同时发表有国家发明专利三项，实用新型专利两项。2014年和2020年分别参与过研发小度智能音响、百度智能云、文心一言等百度集团人工智能产品项目。2020年到2024年分别主导研发过中青林集团、百果园集团、南方电网、SKG集团、安克创新科技等行业领域大模型项目的落地及应用。',
              ]}
            />
          </Carousel>
        </div>
      </section>
      <section className="bg-[#EEF5FD] h-[8.54rem] hidden">
        <SectionTitle
          className="mt-[.98rem] mb-[.44rem]"
          classNames={{ title: 'text-black' }}
          iconColor="#0067FF"
          title="企业活动"
        />
        <div className="flex items-center justify-center gap-[.3rem]">
          <CardProfile3
            onClick={() => history.push('/activity')}
            url={imgAboutJzg1}
            className="w-[4.8rem] h-[4.4rem]"
            classNames={{
              imageContainer: 'h-[2.8rem]',
              title: '!text-[.24rem]',
              content: 'text-[#666666]',
              contentContainer: '!items-start',
            }}
            title={'公司活动标题'}
            content={
              '公司活动详细简介内容，主要展示活动核心内容公司活动详细简介内容，主要展示活动核心内容'
            }
          />
          <CardProfile3
            onClick={() => history.push('/activity')}
            url={imgAboutActivity1}
            className="w-[4.8rem] h-[4.4rem]"
            classNames={{
              imageContainer: 'h-[2.8rem]',
              title: '!text-[.24rem]',
              content: 'text-[#666666]',
              contentContainer: '!items-start',
            }}
            title={'公司活动标题'}
            content={
              '公司活动详细简介内容，主要展示活动核心内容公司活动详细简介内容，主要展示活动核心内容'
            }
          />
          <CardProfile3
            onClick={() => navgate('/activity')}
            url={imgAboutActivity2}
            className="w-[4.8rem] h-[4.4rem]"
            classNames={{
              imageContainer: 'h-[2.8rem]',
              title: '!text-[.24rem]',
              content: 'text-[#666666]',
              contentContainer: '!items-start',
            }}
            title={'公司活动标题'}
            content={
              '公司活动详细简介内容，主要展示活动核心内容公司活动详细简介内容，主要展示活动核心内容'
            }
          />
        </div>
      </section>
    </div>
  );
}
