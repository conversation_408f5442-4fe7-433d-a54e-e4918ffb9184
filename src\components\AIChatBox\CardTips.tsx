/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 23:36:18
 * @LastEditTime: 2024-12-17 20:22:42
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { memo } from 'react';

interface IProps {
  className?: string;
  icon?: React.ReactNode;
  title?: string;
  tips?: string;
}

export const CardTips = memo((props: IProps) => {
  const { icon, tips, title } = props;
  return (
    <section className="flex flex-row bg-[#1E1E27]/70 w-[5.4rem] h-[1.3rem] rounded-[16px] p-[.2rem] gap-[.2rem]">
      <div className="flex flex-col justify-center items-center">{icon}</div>
      <div className="flex flex-col justify-center gap-[.1rem]">
        <p className="text-[#ffffff] text-[.2rem] font-medium">{title}</p>
        <p className="text-[#737373] text-[.16rem] font-normal">{tips}</p>
      </div>
    </section>
  );
});
