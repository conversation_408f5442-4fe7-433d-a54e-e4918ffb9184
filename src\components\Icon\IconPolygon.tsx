import { memo } from 'react';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-15 23:32:45
 * @LastEditTime: 2024-12-17 16:17:18
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  color?: string;
  className?: string;
}

export const IconPolygon = memo((props: IProps) => {
  const { color = '#6EFCD2', className } = props;
  return (
    <svg
      className={className}
      width="12"
      height="10"
      viewBox="0 0 12 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.26795 0.999999C5.03775 -0.333335 6.96225 -0.333333 7.73205 1L11.1962 7C11.966 8.33333 11.0037 10 9.4641 10H2.5359C0.996296 10 0.0340474 8.33333 0.803848 7L4.26795 0.999999Z"
        fill={color}
      />
    </svg>
  );
});
