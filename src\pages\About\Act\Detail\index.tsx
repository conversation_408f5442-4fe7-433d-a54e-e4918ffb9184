import HotTopic from '../HotTopic/HotTopic';
import { ReactComponent as Avatar } from '@/assets/about/Act/avatar.svg';
import list from '../content';
import './index.less';
// const ActContent = styled.div`
//   img{
//     width:8.64rem;
//     height: auto;
//   }
//   p{
//     margin: .3rem auto;
//     font-size: .16rem;
//     height: .26rem;
//     color: #333;
//   }
// `
export default function Detail() {
  const item = list[0];
  return (
    <div className="bg-[#f6f5fa] pb-[1rem] about-act-detail min-h-[800px]">
      <div className="pt-[1.2rem]   w-[13rem] mx-auto">
        <a className="block text-[.18rem] text-[#666] leading-[.3rem]" href="/about/act">
          &lt; 返回
        </a>
        <div className="flex mt-[.20rem]">
          <div className="w-[8.64rem]">
            <div className="text-[.4rem] leading-[.5rem]">{item.title}</div>
            <div className="text-[.16rem] leading-[.24rem] h-[.24rem]  flex items-center mt-[.24rem]">
              <Avatar className="shrink-0 text-[#5188FF]"></Avatar>
              <div className="text-[#333] ml-[.08rem]">{item.author}</div>
              <div className="text-[#999] mx-[.1rem]">|</div>
              <div className="text-[#999]">{item.date}</div>
            </div>
            <div className="" dangerouslySetInnerHTML={{ __html: item.content }} />
          </div>
          <HotTopic></HotTopic>
        </div>
      </div>
    </div>
  );
}
