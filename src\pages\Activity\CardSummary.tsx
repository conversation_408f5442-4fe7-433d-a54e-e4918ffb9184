/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 18:06:07
 * @LastEditTime: 2024-12-16 21:35:42
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { CustomDivider } from '@/components/Divider';
import { IconPolygon2 } from '@/components/Icon/IconPolygon2';
import { ReactNode } from 'react';

interface IProps {
  className?: string;
  title?: string;
  content?: string | ReactNode;
}

export const CardSummary = (props: IProps) => {
  const { className, title, content } = props;

  return (
    <div className={`flex flex-col w-[3.82rem] ${className}`}>
      <div className="text-[.48rem] flex items-center gap-[.14rem] font-semibold">
        <IconPolygon2 />
        {title}
      </div>
      <CustomDivider className="my-[.5rem]" />
      <div className="text-[.16rem] font-normal text-[#666666]">{content}</div>
    </div>
  );
};
