interface IProps {
  className?: string;
}

export const IconMall = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle opacity="0.2" cx="16" cy="16" r="16" fill="white" />
      <path
        d="M17.1623 9.75576L22.181 13.0179C22.3583 13.1332 22.447 13.1908 22.5112 13.2677C22.5681 13.3357 22.6108 13.4145 22.6369 13.4992C22.6663 13.595 22.6663 13.7007 22.6663 13.9122V18.8C22.6663 19.9201 22.6663 20.4802 22.4484 20.908C22.2566 21.2843 21.9506 21.5903 21.5743 21.7821C21.1465 22 20.5864 22 19.4663 22H12.533C11.4129 22 10.8529 22 10.425 21.7821C10.0487 21.5903 9.74274 21.2843 9.55099 20.908C9.33301 20.4802 9.33301 19.9201 9.33301 18.8V13.9122C9.33301 13.7007 9.33301 13.595 9.36245 13.4992C9.38852 13.4145 9.43124 13.3357 9.48811 13.2677C9.55235 13.1908 9.64102 13.1332 9.81835 13.0179L14.837 9.75576M17.1623 9.75576C16.7415 9.48222 16.5311 9.34545 16.3044 9.29224C16.104 9.2452 15.8954 9.2452 15.695 9.29224C15.4683 9.34545 15.2579 9.48222 14.837 9.75576M17.1623 9.75576L21.2904 12.439C21.749 12.7371 21.9783 12.8861 22.0577 13.0751C22.1271 13.2403 22.1271 13.4265 22.0577 13.5916C21.9783 13.7806 21.749 13.9296 21.2904 14.2277L17.1623 16.911C16.7415 17.1845 16.5311 17.3213 16.3044 17.3745C16.104 17.4216 15.8954 17.4216 15.695 17.3745C15.4683 17.3213 15.2579 17.1845 14.837 16.911L10.7089 14.2277C10.2503 13.9296 10.0211 13.7806 9.94166 13.5916C9.87226 13.4265 9.87226 13.2403 9.94166 13.0751C10.0211 12.8861 10.2503 12.7371 10.7089 12.439L14.837 9.75576M22.333 20.6667L17.9045 16.6667M14.0949 16.6667L9.66634 20.6667"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
