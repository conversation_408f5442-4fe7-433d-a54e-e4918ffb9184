<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>消息中心 - 银河星尘</title>
  <link rel="stylesheet" href="styles.css">
  <link href="css/all.min.css" rel="stylesheet">
  <script src="js/gsap.min.js"></script>
  <script src="js/ScrollTrigger.min.js"></script>
</head>

<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="nav-container">
      <div class="nav-logo">
        <img src="company_logo.png" alt="银河星尘" class="logo-image">
      </div>
      <ul class="nav-menu">
        <li class="nav-item">
          <a href="index.html" class="nav-link">首页</a>
        </li>
        <li class="nav-item">
          <a href="products.html" class="nav-link">产品中心</a>
        </li>
        <li class="nav-item dropdown">
          <a href="scenarios.html" class="nav-link">应用场景</a>
          <div class="dropdown-menu">
            <a href="scenarios.html" class="dropdown-item">工地监测</a>
            <a href="scenarios.html" class="dropdown-item">公园管理</a>
            <a href="scenarios.html" class="dropdown-item">垃圾场治理</a>
            <a href="scenarios.html" class="dropdown-item">学校防护</a>
            <a href="scenarios.html" class="dropdown-item">医院环境</a>
            <a href="scenarios.html" class="dropdown-item">园区服务</a>
            <a href="scenarios.html" class="dropdown-item">贸易市场</a>
            <a href="scenarios.html#cases" class="dropdown-item">落地案例</a>
          </div>
        </li>
        <li class="nav-item">
          <a href="news.html" class="nav-link active">新闻中心</a>
        </li>
        <li class="nav-item">
          <a href="company.html" class="nav-link">企业信息</a>
        </li>
      </ul>
      <div class="nav-toggle">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </nav>

  <main class="news-main">
    <!-- 新闻中心头部 -->
    <section class="news-hero slide-in-scale">
      <!-- 浮动的毛玻璃球 -->
      <div class="glass-ball-2"></div>
      <div class="glass-ball-3"></div>
      <div class="glass-ball-4"></div>
      <div class="glass-ball-5"></div>
      <div class="glass-ball-6"></div>

      <div class="container">
        <div class="news-header">
          <div class="header-content">
            <div class="header-left">
              <h1 class="news-title">News</h1>
            </div>
            <div class="header-right">
              <div class="news-info">
                <h2 class="news-info-title">新闻中心</h2>
                <p class="news-description">
                  关注银河星尘最新动态，见证AI技术引领病媒防控未来。<strong
                    class="highlight-text">从技术突破到行业合作，从产品发布到应用案例，我们与您分享智慧防控的每一个重要时刻。</strong>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 消息列表 -->
    <section class="news-list">
      <div class="container">
        <div class="news-list-container" id="news-list-container">
          <!-- 新闻列表由js/news-list.js动态渲染 -->
        </div>
        <div class="pagination" id="pagination"></div>
      </div>
    </section>
  </main>

  <div id="footer-container"></div>
  <script src="js/footer.js"></script>


  <script src="js/common.js"></script>
  <script src="js/news-list.js"></script>
  <script>

  </script>
</body>

</html>
