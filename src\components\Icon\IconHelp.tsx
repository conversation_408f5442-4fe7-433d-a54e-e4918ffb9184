/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-16 11:02:27
 * @LastEditTime: 2024-12-16 11:02:45
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}
export const IconHelp = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="30"
      height="30"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.0287 3.03959C14.3832 2.84264 14.5605 2.74417 14.7482 2.70556C14.9143 2.67139 15.0857 2.67139 15.2518 2.70556C15.4395 2.74417 15.6168 2.84264 15.9713 3.03959L25.2213 8.17847C25.5957 8.38648 25.7829 8.49048 25.9192 8.6384C26.0398 8.76926 26.1311 8.92437 26.1869 9.09334C26.25 9.28433 26.25 9.49849 26.25 9.92679V20.0732C26.25 20.5015 26.25 20.7156 26.1869 20.9066C26.1311 21.0756 26.0398 21.2307 25.9192 21.3616C25.7829 21.5095 25.5957 21.6135 25.2213 21.8215L15.9713 26.9604C15.6168 27.1573 15.4395 27.2558 15.2518 27.2944C15.0857 27.3286 14.9143 27.3286 14.7482 27.2944C14.5605 27.2558 14.3832 27.1573 14.0287 26.9604L4.77871 21.8215C4.40431 21.6135 4.21711 21.5095 4.08079 21.3616C3.9602 21.2307 3.86893 21.0756 3.81311 20.9066C3.75 20.7156 3.75 20.5015 3.75 20.0732V9.92679C3.75 9.49849 3.75 9.28433 3.81311 9.09334C3.86893 8.92437 3.9602 8.76926 4.08079 8.6384C4.21711 8.49048 4.40431 8.38648 4.77871 8.17848L14.0287 3.03959Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.9957 11.3349C13.7461 9.87398 11.6623 9.48101 10.0967 10.8187C8.53103 12.1565 8.3106 14.3931 9.54012 15.9752C10.3276 16.9886 12.3445 18.8678 13.6928 20.0879C14.1409 20.4934 14.3649 20.6961 14.6332 20.7775C14.8639 20.8474 15.1275 20.8474 15.3582 20.7775C15.6266 20.6961 15.8506 20.4934 16.2986 20.0879C17.647 18.8678 19.6638 16.9886 20.4513 15.9752C21.6808 14.3931 21.4873 12.1424 19.8948 10.8187C18.3022 9.49508 16.2453 9.87398 14.9957 11.3349Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
