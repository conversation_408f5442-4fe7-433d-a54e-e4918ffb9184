// js/common.js
// 通用动画、导航、二维码弹窗、轮播、平滑滚动等函数
// 仅包含所有页面都需要的功能

// 注册GSAP插件
if (window.gsap && window.ScrollTrigger) {
  gsap.registerPlugin(ScrollTrigger);
}

// 工具函数
function throttle(func, limit) {
  let inThrottle;
  return function () {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// 平滑滚动到指定元素
function scrollToElement(elementId) {
  const element = document.querySelector(elementId);
  if (element && window.gsap) {
    gsap.to(window, {
      duration: 1,
      scrollTo: element,
      ease: "power2.inOut",
    });
  } else if (element) {
    element.scrollIntoView({ behavior: "smooth", block: "start" });
  }
}

// 公共导航栏交互
function initNavigation() {
  const navbar = document.querySelector(".navbar");
  const navToggle = document.querySelector(".nav-toggle");
  const navMenu = document.querySelector(".nav-menu");
  const navLinks = document.querySelectorAll(".nav-link");

  // 滚动时导航栏效果
  window.addEventListener(
    "scroll",
    throttle(() => {
      const logoImage = document.querySelector(".logo-image");
      if (window.scrollY > 100) {
        navbar.classList.add("scrolled");
        if (logoImage) logoImage.src = "company_logo.png";
      } else {
        navbar.classList.remove("scrolled");
        if (logoImage) logoImage.src = "company_logo.png";
      }
    }, 100)
  );

  // 移动端菜单切换
  if (navToggle) {
    navToggle.addEventListener("click", () => {
      navMenu.classList.toggle("active");
    });
  }

  // 平滑滚动和高亮当前导航
  navLinks.forEach((link) => {
    link.addEventListener("click", (e) => {
      const href = link.getAttribute("href");
      if (href && href.startsWith("#")) {
        e.preventDefault();
        const target = document.querySelector(href);
        if (target) {
          scrollToElement(href);
          if (navMenu.classList.contains("active")) navToggle.click();
        }
      }
    });
  });
}

document.addEventListener("DOMContentLoaded", initNavigation);

// 页面动画（如有需要可继续补充）
function pageAnimate() {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in');
      } else {
        entry.target.classList.remove('animate-in');
      }
    });
  }, {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  });
  const animatedElements = document.querySelectorAll('.page-section, .slide-in-left, .slide-in-right, .slide-in-scale');
  animatedElements.forEach(el => {
    observer.observe(el);
  });
}
document.addEventListener('DOMContentLoaded', pageAnimate);

// 二维码弹窗（如有）
window.openQRCodeModal = function (src, alt) {
  const modal = document.getElementById("qrcodeModal");
  const img = document.getElementById("qrcodeModalImg");
  if (img && modal) {
    img.src = src;
    img.alt = alt;
    modal.classList.add("show");
    document.body.style.overflow = "hidden";
  }
};
window.closeQRCodeModal = function () {
  const modal = document.getElementById("qrcodeModal");
  if (modal) {
    modal.classList.remove("show");
    document.body.style.overflow = "";
  }
};
