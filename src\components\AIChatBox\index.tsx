/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-30 13:37:31
 * @LastEditTime: 2025-01-09 14:07:37
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { chatAPI } from '@/api';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { useRequest } from '@umijs/max';
import { ConfigProvider, Input, Upload } from 'antd';
import * as nano from 'nanoid';
import { memo, useCallback, useEffect, useMemo, useState, useTransition } from 'react';
import { QuickTag } from './components/QuickTag';
import { DefaultChat } from './DefaultChat';
import { IconAnotation } from './icons/IconAnotation';
import { IconFileAttachment } from './icons/IconFileAttachment';
import { IconFileCode } from './icons/IconFileCode';
import { IconNavigationPointer } from './icons/IconNavigationPointer';
import { IconPhoto2 } from './icons/IconPhoto2';
import { IconPresentation } from './icons/IconPresentation';
import { MessageBox } from './MessageBox';

const { TextArea } = Input;
const { nanoid } = nano;

export interface IMessage {
  timestamp?: number;
  nickname?: string;
  person?: 'primary' | 'secondary';
  text: string;
}

const host = process.env.NODE_ENV === 'development' ? 'http://************:8001/api' : '/api';
console.log('🚀 ~ host:', host);

export const AIChatBox = memo(() => {
  const [messageList, setMessageList] = useState<IMessage[]>([]);
  const [sendMsg, setSendMsg] = useState('');
  const uniqueId = useMemo(() => nanoid(), []);
  const [, startTransition] = useTransition();
  const ctrl = new AbortController();
  // a回答， q问问题
  const [qa, setQA] = useState<'Q' | 'A' | null>();

  // 上传图片
  const startUploadFile = useRequest(
    (data?: any, headers?: any) => ({
      url: chatAPI.chat,
      method: 'POST',
      data: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
    },
  );
  // 发消息
  const onSendMsg = useCallback(() => {
    if (qa === 'A') {
      return;
    }
    if (sendMsg.trim() === '') {
      return;
    }
    setQA('A');
    setSendMsg('');

    if (sendMsg && sendMsg.trim() !== '') {
      if (sendMsg) {
        let prv = messageList.slice();
        prv = prv.concat(
          { text: sendMsg, person: 'primary', nickname: '管理员', timestamp: Date.now() },
          { text: '...', person: 'secondary', nickname: '星小尘', timestamp: Date.now() },
        );
        setMessageList(prv);

        fetchEventSource(`${host}${chatAPI.chat}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            session_id: uniqueId,
            question: sendMsg,
            stream: true,
          }),
          signal: ctrl.signal,
          async onopen(response) {
            console.log('🚀 ~ onopen', response.status);
          },
          onmessage(msg) {
            let m = JSON.parse(msg.data);
            const np = prv.slice();
            const lastItem = np[np.length - 1];

            if (lastItem.text === '...') {
              lastItem.text = '';
            }
            lastItem.text += m.content;

            startTransition(() => {
              setMessageList(np);
            });
          },
          onclose() {
            console.log('🚀 ~onclose:');
            setQA(null);
            ctrl.abort();
          },
          onerror(err) {
            console.log('🚀 ~ err:', err);
            throw err;
          },
        });
      }
    }
  }, [sendMsg, messageList]);
  // 发图片
  const onSendImg = useCallback(async (img: string) => {
    if (img) {
      setMessageList((p) => [
        ...p,
        { text: img, person: 'primary', nickname: '管理员', timestamp: Date.now() },
      ]);
      await startUploadFile.run({
        session_id: uniqueId,
        image_url: img,
        stream: false,
      });
    }
  }, []);

  const onPressEnter = useCallback(
    (e: any) => {
      if (qa === 'A') {
        return;
      }
      e.preventDefault();
      onSendMsg();
    },
    [qa, sendMsg],
  );
  const onChangeText = useCallback((e: any) => setSendMsg(e.target.value), []);
  const beforeUpload = async (file: File) => {
    await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const base64Str = reader.result;

        onSendImg(base64Str as string);
        resolve({ base64: base64Str, name: file.name });
      };
      reader.onerror = (error) => reject(error);
    });
    return false;
  };

  useEffect(() => {
    return () => {
      ctrl.abort();
    };
  }, []);

  useEffect(() => {
    startTransition(() => {
      const messageSection = document.getElementById('msg' + uniqueId);
      messageSection?.scrollTo(0, messageSection.scrollHeight);
    });
  }, [messageList]);

  return (
    <>
      {/* message 68px是左侧标题title 高度 */}
      <div className="flex w-[11rem] h-[calc(100vh-68px-2.16rem)] mt-[60px] ">
        {messageList.length === 0 ? (
          <DefaultChat />
        ) : (
          <MessageBox messageList={messageList} uniqueId={uniqueId} />
        )}
      </div>

      {/* 操作区 */}
      <div className={'absolute bottom-[.36rem] left-[.2rem] right-0 h-[1.34rem]'}>
        {/* 快捷指令 */}
        <div className="flex gap-[.08rem] text-[.16rem] font-normal text-white h-[.36rem] mb-[.1rem]">
          <QuickTag icon={<IconAnotation className="size-[.16rem]" />} title="病媒知识答疑" />
          <QuickTag icon={<IconPresentation className="size-[.16rem]" />} title="智能监测" />
          <QuickTag icon={<IconFileCode className="size-[.16rem]" />} title="智能决策" />
          <QuickTag icon={<IconFileAttachment className="size-[.16rem]" />} title="信息梳理" />
        </div>
        <div className="relative h-[.88rem]">
          {/* 输入框 */}
          <ConfigProvider
            theme={{
              components: {
                Input: {
                  colorTextPlaceholder: '#999999',
                  colorBgContainer: '#292933',
                  colorText: '#ffffff',
                },
              },
            }}
          >
            <TextArea
              value={sendMsg}
              autoFocus={true}
              onChange={onChangeText}
              placeholder="输入您想了解的内容，Enter发送"
              disabled={qa === 'A'}
              classNames={{
                textarea: `!h-[100%] !align-top`,
              }}
              onPressEnter={onPressEnter}
            >
              <p></p>
            </TextArea>
          </ConfigProvider>
          {/* 按钮区 */}
          <div className="absolute top-[50%] translate-y-[-50%] right-[.2rem] h-[.50rem] flex flex-row justify-center items-center">
            <Upload
              disabled={qa === 'A'}
              beforeUpload={beforeUpload}
              maxCount={1}
              showUploadList={false}
              accept=".png, .jpg, .jpeg"
              className="w-[.32rem] h-[.32rem] flex flex-col items-center justify-center "
            >
              <div className={`flex justify-center items-center w-[.20rem] h-[.20rem]`}>
                <IconPhoto2 className="size-[.2rem]" />
              </div>
            </Upload>
            <div
              className={`flex justify-center items-center w-[.50rem] h-[.50rem] rounded-[.25rem] ml-[.2rem] ${
                sendMsg.length > 0 ? 'bg-[#2B65F6]' : 'bg-[#5A5D64]'
              }`}
              onClick={onSendMsg}
            >
              <div className={`flex justify-center items-center w-[.20rem] h-[.20rem]`}>
                <IconNavigationPointer className="size-[.2rem]" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
});
