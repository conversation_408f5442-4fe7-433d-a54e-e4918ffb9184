import { memo } from 'react';
import imgDefault from './assets/img_default.png';
import { CardTips } from './CardTips';
import { IconBarChart } from './icons/IconBarChart';
import { IconBellRinging } from './icons/IconBellRinging';
import { IconLightbulb } from './icons/IconLightbulb';
import { IconMultipleModal } from './icons/IconMultipleModal';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-12 17:55:23
 * @LastEditTime: 2024-12-18 16:23:51
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description:
 */
export const DefaultChat = memo(() => {
  return (
    <div className="flex flex-col gap-[.2rem]">
      <section className="flex flex-col items-center text-[.28rem] text-[#ffffff] font-medium text-center sm:mb-[.2rem] md:mb-[.2rem] 2xl:mb-[.6rem]">
        <img src={imgDefault} className="w-[1.83rem] h-[1.67rem]" />
        <div className="mt-[.16rem] text-[.44rem] font-semibold">开启您的病媒智能监测之旅</div>
        <div className="rounded-[.16rem] py-[.1rem] px-[.2rem] text-[.16rem] text-[#C8C8C8] bg-[#292933] mt-[.2rem] font-normal">
          上传资料，体验精准快捷的监测服务
        </div>
      </section>

      <section className="flex flex-row gap-[.2rem]">
        <CardTips
          icon={<IconMultipleModal className="size-[.24rem]" />}
          title="多模态输入"
          tips="实现图片、音频、文件上传识别病媒种类"
        />
        <CardTips
          icon={<IconBarChart className="size-[.24rem]" />}
          title="数据分析"
          tips="对上传的数据进行分析，提供相关分布、密度等信息"
        />
      </section>

      <section className="flex flex-row gap-[.2rem]">
        <CardTips
          icon={<IconLightbulb className="size-[.24rem]" />}
          title="智能问答"
          tips="通过问题咨询，调用知识库回答病媒生物相关情况"
        />
        <CardTips
          icon={<IconBellRinging className="size-[.24rem]" />}
          title="智能预警"
          tips="通过上传相关检测数据，预测可能爆发病害的目的地"
        />
      </section>
    </div>
  );
});
