/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 01:30:08
 * @LastEditTime: 2024-12-18 16:10:14
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}

export const IconLightning = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.66662 1.33337L2.72892 8.45861C2.49638 8.73766 2.38011 8.87718 2.37833 8.99502C2.37679 9.09745 2.42244 9.19491 2.50212 9.2593C2.59378 9.33337 2.7754 9.33337 3.13864 9.33337H7.99995L7.33328 14.6667L13.271 7.54147C13.5035 7.26243 13.6198 7.1229 13.6216 7.00507C13.6231 6.90263 13.5775 6.80517 13.4978 6.74078C13.4061 6.66671 13.2245 6.66671 12.8613 6.66671H7.99995L8.66662 1.33337Z"
        stroke="black"
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
