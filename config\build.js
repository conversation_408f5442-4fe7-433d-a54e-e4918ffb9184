const fs = require('fs');
const { execSync } = require('child_process');

// 删除 dist 目录
if (fs.existsSync('dist')) {
  try {
    fs.rmSync('dist', { recursive: true, force: true });
    console.log('Deleted dist directory');
  } catch (err) {
    console.error('Error deleting dist directory:', err);
    process.exit(1);
  }
}

// 创建 dist 目录
try {
  fs.mkdirSync('dist');
  console.log('Created dist directory');
} catch (err) {
  console.error('Error creating dist directory:', err);
  process.exit(1);
}

// 复制 public 目录下的文件到 dist
try {
  execSync('copyfiles -u 1 "public/new/**/*" dist --up 1', { stdio: 'inherit' });
  console.log('Copied public files to dist');
} catch (err) {
  console.error('Error copying files:', err);
  process.exit(1);
}

console.log('Build completed successfully');
