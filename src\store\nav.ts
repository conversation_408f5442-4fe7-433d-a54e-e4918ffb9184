import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-19 15:04:09
 * @LastEditTime: 2024-12-19 16:10:34
 * @LastEditors: <PERSON><PERSON>
 * @Description: x
 */

interface navStore {
  tab: string;
  dropMenusKeys: string[];
  updateTab: (tab: string) => void;
  updateDropMenusKeys: (dropMenusKeys: string[]) => void;
}

export const useNavStore = create(
  persist<navStore>(
    (set) => ({
      dropMenusKeys: [],
      tab: '/home',
      updateTab: (tab: string) => set({ tab }),
      updateDropMenusKeys: (dropMenusKeys: string[]) => set({ dropMenusKeys }),
    }),
    {
      name: 'xxc-storage', // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => sessionStorage), // (optional) by default, 'localStorage' is used
    },
  ),
);
