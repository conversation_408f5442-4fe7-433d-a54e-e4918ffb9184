/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-17 00:16:54
 * @LastEditTime: 2024-12-17 00:17:03
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}
export const IconFileCode = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.33366 12.3333C3.33366 12.6429 3.33366 12.7977 3.35077 12.9276C3.46892 13.8251 4.17514 14.5313 5.07261 14.6495C5.20257 14.6666 5.35738 14.6666 5.66699 14.6666H10.8003C11.9204 14.6666 12.4805 14.6666 12.9083 14.4486C13.2846 14.2569 13.5906 13.9509 13.7823 13.5746C14.0003 13.1467 14.0003 12.5867 14.0003 11.4666V6.65874C14.0003 6.16955 14.0003 5.92496 13.9451 5.69479C13.8961 5.49072 13.8153 5.29563 13.7056 5.11668C13.5819 4.91485 13.409 4.74189 13.0631 4.39599L10.9376 2.27051C10.5917 1.92461 10.4187 1.75166 10.2169 1.62797C10.038 1.51831 9.84286 1.43751 9.63879 1.38851C9.40861 1.33325 9.16402 1.33325 8.67484 1.33325H5.66699C5.35738 1.33325 5.20257 1.33325 5.07261 1.35036C4.17514 1.46852 3.46892 2.17473 3.35077 3.0722C3.33366 3.20217 3.33366 3.35697 3.33366 3.66659M6.00033 9.66659L7.66699 7.99992L6.00033 6.33325M3.33366 6.33325L1.66699 7.99992L3.33366 9.66659"
        stroke="#999999"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
