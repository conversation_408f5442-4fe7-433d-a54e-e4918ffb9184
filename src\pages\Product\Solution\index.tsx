import imgSolution from '@/assets/solution/img_solution_1.webp';
import imgSolution2 from '@/assets/solution/img_solution_2.jpg';
import imgSolution3 from '@/assets/solution/img_solution_3.jpg';
import imgSolution4 from '@/assets/solution/img_solution_4.jpg';
import imgSolution5 from '@/assets/solution/img_solution_5.jpg';
import { PicShow } from '@/components/PicShow';
import { SectionTitle } from '@/components/SectionTips';
import { XcTag } from '../../../components/Tag';
import { PopTips } from './PopTips';

export default function Solution() {
  const tag = [
    {
      key: '1',
      title: '星尘AS平台',
    },
    {
      key: '2',
      title: '物联网硬件',
    },
    {
      key: '3',
      title: '大模型',
    },
    {
      key: '4',
      title: '病媒生物专家',
    },
  ];

  return (
    <div className="flex flex-col w-full">
      <header
        className="flex flex-row w-full h-[8.58rem] pl-[2rem]"
        style={{
          backgroundImage: `url(${imgSolution})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'contain',
        }}
      >
        <div className="flex flex-col w-1/2 h-full justify-center text-[.64rem] text-white font-semibold">
          <p className="">病媒数字</p>
          <p className="">
            <span>监测</span>
            <span className="text-[#0067FF]">解决方案</span>
          </p>
        </div>
        <div className="relative flex flex-col w-1/2">
          <PopTips tips="Aiaas平台" className="absolute top-[1.78rem] right-[6.4rem]" />
          <PopTips tips="病媒大模型" className="absolute top-[3.05rem] right-[4.45rem]" />
          <PopTips tips="病媒专家经验" className="absolute top-[4.84rem] right-[6.65rem]" />
          <PopTips
            tips="物联网（IoT）硬件"
            className="absolute top-[4.7rem] right-[2.68rem] w-[2.85rem]"
          />
        </div>
      </header>
      <section className="bg-white w-full h-[9.47rem] px-[2rem] flex flex-row ">
        <div className="flex flex-row items-center mt-[1.71rem] w-full h-[6rem]">
          <div className="w-[6.18rem] h-[6rem] bg-[#E1EEFF] p-[.5rem] flex flex-col justify-between">
            <div className="flex flex-col">
              <div className="text-[.48rem] font-medium text-black mb-[.12rem]">
                <span>
                  银河<span className="text-[#0067FF]">数字</span>监测解决方案
                </span>
              </div>
              <div className="flex gap-[.08rem]">
                {tag.map((item) => {
                  return <XcTag title={item.title} key={item.key} />;
                })}
              </div>
            </div>
            <div className="text-[.16rem] font-normal leading-[.32rem] w-[4.7rem] mx-auto">
              银河星尘采用最先进的技术手段，融合AI病媒大模型及智能物联网（IoT）设备，结合地理信息系统（GIS）、深度学习及数据分析技术，构建了一套全面的病媒监测预警系统。该系统能够实时监测病媒生物的分布情况、活动规律和密度变化，为用户提供精准的预警服务。
            </div>
          </div>
          <img src={imgSolution2} className="w-[9.02rem] h-[6rem]" />
        </div>
      </section>
      <section
        className=" w-full h-[9.45rem] flex flex-col px-[2rem]"
        style={{
          backgroundImage: `url(${imgSolution5})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
        }}
      >
        <SectionTitle className="mt-[1.2rem] mb-[.8rem]" title="服务优势" />
        <div className="flex gap-[.6em]">
          <PicShow
            url={imgSolution3}
            title="病媒生物监测服务"
            content="病媒生物监测服务更精准、全面的监测鼠类、蚊虫的种类、数量、分布密度及监测报告等数据"
          />
          <PicShow
            url={imgSolution4}
            title="疾病预测服务"
            content="环境疾病爆发风险预测、病媒监测、活动及控制效果预测、公共发布数据风险、趋势预测等"
          />
        </div>
      </section>
    </div>
  );
}
