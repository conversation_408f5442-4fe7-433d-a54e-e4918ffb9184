interface IProps {
  className?: string;
}

export const IconBarLine = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.6667 16.6667V10.8333M10 16.6667V8.33333M3.33337 16.6667L3.33337 13.3333M11.1723 4.18959L15.4793 5.80472M8.99902 4.50077L4.33356 7.99986M17.5506 5.36612C18.0387 5.85427 18.0387 6.64573 17.5506 7.13388C17.0624 7.62204 16.271 7.62204 15.7828 7.13388C15.2947 6.64573 15.2947 5.85427 15.7828 5.36612C16.271 4.87796 17.0624 4.87796 17.5506 5.36612ZM4.21726 7.86612C4.70541 8.35427 4.70541 9.14573 4.21726 9.63388C3.7291 10.122 2.93765 10.122 2.44949 9.63388C1.96134 9.14573 1.96134 8.35427 2.44949 7.86612C2.93765 7.37796 3.7291 7.37796 4.21726 7.86612ZM10.8839 2.86612C11.3721 3.35427 11.3721 4.14573 10.8839 4.63388C10.3958 5.12204 9.60431 5.12204 9.11616 4.63388C8.628 4.14573 8.628 3.35427 9.11616 2.86612C9.60431 2.37796 10.3958 2.37796 10.8839 2.86612Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
