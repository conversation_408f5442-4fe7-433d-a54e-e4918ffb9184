// js/index.js
// 首页专用脚本，仅保留首页相关动画、数据渲染、发展历程timeline渲染等

// 发展历程时间线数据
const timelineData = [
  { date: "2024年8月", title: "公司成立", description: "银河星尘正式成立，并确定公司研发方向及市场重点" },
  { date: "2024年10月", title: "战略合作关系建立", description: "银河星尘与国家健康医疗大数据研究院（深圳）、香港中文大学（深圳）成立战略合作关系" },
  { date: "2024年11月", title: "与cdc传染病所共同研发病媒AI监测平台", description: "银河星尘与中国疾病预防控制中心传染病预防控制所共同研发病媒AI监测平台，携手国家CDC重点实验室，共探病媒生物智能防控新征程" },
  { date: "2024年12月", title: "深圳市疾控共建数智防线", description: "银河星尘同深圳市疾控共建数智防线，引领病媒监测新范式" },
  { date: "2025年3月15日", title: "病媒AI大模型监测平台和AI硬件发布", description: "银河星尘在病媒融合创新大会正式发布AI大模型监测平台和AI硬件产品" },
  { date: "2025年4月25日", title: "中国疾控中心专家莅临指导产品发展方向", description: "中国疾控中心专家对公司成员进行病媒知识培训并指导产品发展方向" },
  { date: "2025年06月06日", title: "华中科技大学研讨合作", description: "银河星尘与华中科技大学展开人工智能在生态环境领域应用研讨" },
  { date: "2025年06月28日", title: "疾控中心专家深度交流", description: "中国疾控中心专家亲临银河星尘，共探AI赋能病媒监测新突破" },
  { date: "2025年07月01日", title: "病媒AI系统亮相中博会", description: "银河星尘病媒 AI 大模型监测系统及设备亮相中博会" },
  { date: "2025年7月15日", title: "银河星尘亮相2025全国卫生杀虫药械学术交流会", description: "中国疾控中心病媒生物首席专家刘起勇研究员首次以银河星尘的“星尘AI+病媒监测平台”真实数据与界面演讲公开演讲《AI赋能下蚊媒监测预警与控制》" }
];

function renderTimeline() {
  const container = document.getElementById("timeline-items-container");
  if (!container) return;
  container.innerHTML = "";
  timelineData.forEach((item) => {
    const div = document.createElement("div");
    div.className = "timeline-item";
    div.innerHTML = `
      <div class="timeline-marker"></div>
      <div class="timeline-card">
        <div class="timeline-date">${item.date}</div>
        <h3 class="timeline-title">${item.title}</h3>
        <p class="timeline-description">${item.description}</p>
      </div>
    `;
    container.appendChild(div);
  });
}

// 首页首屏三阶段切换与滚动进度动画
function initHomeHeroAnimation() {
  const heroVideoSection = document.querySelector(
    ".hero-video-section"
  );
  const initialContent = document.querySelector(
    ".hero-initial-content"
  );
  const scrollContent = document.querySelector(".hero-scroll-content");
  const thirdContent = document.querySelector(".hero-third-content");
  const progressFill = document.querySelector(".progress-fill");

  let currentStage = 0; // 0: 初始, 1: 第二模块, 2: 第三模块
  let isAutoPlaying = true; // 是否在自动播放阶段
  let hasCompletedAutoPlay = false; // 是否已完成自动播放
  let isInFirstScreen = true; // 是否在第一屏

  const firstThreshold = 250;
  const secondThreshold = 600;
  const completeThreshold = 1000;

  // 切换到指定阶段
  function switchToStage(stage) {
    if (stage === 1 && currentStage !== 1) {
      initialContent.classList.add("fade-out");
      scrollContent.classList.add("fade-in");
      scrollContent.classList.remove("slide-out-up", "slide-out-down");
      thirdContent.classList.add("slide-out-down");
      thirdContent.classList.remove("fade-in");
      currentStage = 1;
    } else if (stage === 2 && currentStage !== 2) {
      initialContent.classList.add("fade-out");
      scrollContent.classList.add("slide-out-up");
      scrollContent.classList.remove("fade-in");
      thirdContent.classList.add("fade-in");
      thirdContent.classList.remove("slide-out-down");
      currentStage = 2;
    } else if (stage === 0 && currentStage !== 0) {
      initialContent.classList.remove("fade-out");
      scrollContent.classList.add("slide-out-down");
      scrollContent.classList.remove("fade-in");
      thirdContent.classList.add("slide-out-down");
      thirdContent.classList.remove("fade-in");
      currentStage = 0;
    }
  }

  // 自动播放三个阶段（仅初次进入时）
  function startAutoPlay() {
    setTimeout(() => {
      if (isAutoPlaying) {
        switchToStage(1);
      }
    }, 3000);
    setTimeout(() => {
      if (isAutoPlaying) {
        switchToStage(2);
      }
    }, 6000);
    setTimeout(() => {
      if (isAutoPlaying) {
        hasCompletedAutoPlay = true;
        isAutoPlaying = false;
        isInFirstScreen = false;
        const solutionsSection = document.querySelector(".solutions-section");
        if (solutionsSection) {
          solutionsSection.scrollIntoView({ behavior: "smooth" });
        }
      }
    }, 9000);
  }

  // 处理滚动事件
  function handleScroll() {
    const scrollY = window.scrollY;
    const heroHeight = window.innerHeight;
    if (isAutoPlaying) {
      return;
    }
    if (scrollY < heroHeight) {
      isInFirstScreen = true;
      if (scrollY >= secondThreshold && currentStage !== 2) {
        switchToStage(2);
      } else if (
        scrollY >= firstThreshold &&
        scrollY < secondThreshold &&
        currentStage !== 1
      ) {
        switchToStage(1);
      } else if (scrollY < firstThreshold && currentStage !== 0) {
        switchToStage(0);
      }
      heroVideoSection.style.position = "fixed";
      heroVideoSection.style.top = "0";
      heroVideoSection.style.opacity = "1";
    } else {
      isInFirstScreen = false;
      heroVideoSection.style.position = "absolute";
      heroVideoSection.style.top = "0";
      const fadeDistance = heroHeight * 0.3;
      const fadeProgress = Math.min(
        (scrollY - heroHeight) / fadeDistance,
        1
      );
      heroVideoSection.style.opacity = 1 - fadeProgress;
    }
    if (progressFill && isInFirstScreen) {
      const scrollProgress = Math.min(
        (scrollY / completeThreshold) * 100,
        100
      );
      progressFill.style.width = scrollProgress + "%";
    }
  }

  window.addEventListener("scroll", handleScroll);
  window.addEventListener(
    "wheel",
    function (e) {
      if (isAutoPlaying) {
        e.preventDefault();
      }
    },
    { passive: false }
  );
  window.addEventListener(
    "touchmove",
    function (e) {
      if (isAutoPlaying) {
        e.preventDefault();
      }
    },
    { passive: false }
  );
  startAutoPlay();
  handleScroll();
}

// 发展历程自动横向滚动（Carousel）
function initTimelineCarousel() {
  const container = document.getElementById('timeline-items-container');
  if (!container) return;

  let autoScrollInterval;
  const itemWidth = 340; // 估算每个timeline-item宽度+margin
  const scrollStep = itemWidth;
  const scrollDelay = 2500;
  let started = false;

  // 新增：判断是否为移动端（<=768px），切换纵向滚动
  function isMobile() {
    return window.innerWidth <= 768;
  }

  function startAutoScroll() {
    if (started) return;
    started = true;
    autoScrollInterval = setInterval(() => {
      if (isMobile()) {
        // 纵向滚动，每次200px
        if (container.scrollTop + container.offsetHeight >= container.scrollHeight - 2) {
          container.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          container.scrollBy({ top: 220, behavior: 'smooth' });
        }
      } else {
        // 横向滚动
        if (container.scrollLeft + container.offsetWidth >= container.scrollWidth - 2) {
          container.scrollTo({ left: 0, behavior: 'smooth' });
        } else {
          container.scrollBy({ left: scrollStep, behavior: 'smooth' });
        }
      }
    }, scrollDelay);
  }
  function stopAutoScroll() {
    if (autoScrollInterval) clearInterval(autoScrollInterval);
    started = false;
  }
  container.addEventListener('mouseenter', stopAutoScroll);
  container.addEventListener('mouseleave', startAutoScroll);
  container.style.overflowX = isMobile() ? 'hidden' : 'auto';
  container.style.overflowY = isMobile() ? 'auto' : 'hidden';
  container.style.scrollBehavior = 'smooth';

  // 响应式切换横/纵向滚动
  window.addEventListener('resize', () => {
    container.style.overflowX = isMobile() ? 'hidden' : 'auto';
    container.style.overflowY = isMobile() ? 'auto' : 'hidden';
  });

  // 仅当timeline区域进入视口时才启动自动滚动
  const timelineSection = document.querySelector('.timeline-section');
  if (timelineSection && window.IntersectionObserver) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          startAutoScroll();
        } else {
          stopAutoScroll();
        }
      });
    }, { threshold: 0.2 });
    observer.observe(timelineSection);
  } else {
    // 兼容性兜底，直接启动
    startAutoScroll();
  }
}

document.addEventListener("DOMContentLoaded", () => {
  renderTimeline();
  initHomeHeroAnimation();
  setTimeout(() => {
    initTimelineCarousel();
  }, 500); // 确保timeline渲染后再启动carousel
});
