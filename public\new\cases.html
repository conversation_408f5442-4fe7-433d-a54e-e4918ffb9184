<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>落地案例 - 银河星尘</title>
    <link rel="stylesheet" href="styles.css">
    <link href="css/all.min.css" rel="stylesheet">
    <script src="js/gsap.min.js"></script>
    <script src="js/ScrollTrigger.min.js"></script>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="company_logo.png" alt="银河星尘" class="logo-image">
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">首页</a>
                </li>
                <li class="nav-item">
                    <a href="products.html" class="nav-link">产品中心</a>
                </li>
                <li class="nav-item dropdown">
                    <a href="scenarios.html" class="nav-link active">应用场景</a>
                    <div class="dropdown-menu">
                        <a href="scenarios.html" class="dropdown-item">工地监测</a>
                        <a href="scenarios.html" class="dropdown-item">公园管理</a>
                        <a href="scenarios.html" class="dropdown-item">垃圾场治理</a>
                        <a href="scenarios.html" class="dropdown-item">学校防护</a>
                        <a href="scenarios.html" class="dropdown-item">医院环境</a>
                        <a href="scenarios.html" class="dropdown-item">园区服务</a>
                        <a href="scenarios.html" class="dropdown-item">贸易市场</a>
                        <a href="scenarios.html#cases" class="dropdown-item active">落地案例</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="news.html" class="nav-link">新闻中心</a>
                </li>
                <li class="nav-item">
                    <a href="company.html" class="nav-link">企业信息</a>
                </li>
            </ul>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <main class="cases-main">
        <!-- 案例页面头部 -->
        <section class="cases-hero slide-in-scale">
            <!-- 浮动的毛玻璃球 -->
            <div class="glass-ball-2"></div>
            <div class="glass-ball-3"></div>
            <div class="glass-ball-4"></div>
            <div class="glass-ball-5"></div>
            <div class="glass-ball-6"></div>

            <div class="container">
                <div class="cases-header">
                    <div class="header-content">
                        <div class="header-left">
                            <h1 class="cases-title">Our Cases</h1>
                        </div>
                        <div class="header-right">
                            <div class="cases-info">
                                <h2 class="cases-subtitle">落地案例</h2>
                                <p class="cases-description">
                                    银河星尘凭借先进的AI智能识别技术，为医疗机构、政府部门、生态景区、建筑工地、城市公园等各类场景提供专业的病媒生物监测服务。<strong class="highlight-text">专注于病媒种类识别、密度分析、活动节律监测和预警预测，为客户提供精准的数据支撑。</strong>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 案例列表 -->
        <section class="cases-list">
            <div class="container">
                <div class="cases-grid">
                    <!-- 案例1：医院监测项目 -->
                <div class="case-item">
                    <div class="case-content">
                        <div class="case-header">
                            <div class="case-title-row">
                                <h2 class="case-title">**医院虫媒密度数字监测项目</h2>
                                <div class="case-category">医疗机构</div>
                            </div>
                        </div>
                        <div class="case-row">
                            <div class="case-overview">
                                <h3>项目概述</h3>
                                <p>
                                    本项目对**医院及其周边区域开展系统性病媒生物监测，
                                    全面掌握主要蚊虫种类、数量、性别分布及生境特征，
                                    建立医院专属的病媒生物基础数据库，为科学防控提供数据支撑。
                                </p>
                            </div>

                            <div class="case-content-grid">
                                <div class="case-image">
                                    <div class="image-carousel" data-carousel="hospital">
                                        <div class="carousel-container">
                                            <div class="carousel-slide active">
                                                <img src="assets/land_case/hospital/1.jpg" alt="医院监测项目" class="project-image">
                                            </div>
                                            <div class="carousel-slide">
                                                <img src="assets/land_case/hospital/2.jpg" alt="医院监测项目" class="project-image">
                                            </div>
                                            <div class="carousel-slide">
                                                <img src="assets/land_case/hospital/3.jpg" alt="医院监测项目" class="project-image">
                                            </div>
                                        </div>
                                        <div class="carousel-dots">
                                            <span class="dot active" onclick="currentSlide('hospital', 1)"></span>
                                            <span class="dot" onclick="currentSlide('hospital', 2)"></span>
                                            <span class="dot" onclick="currentSlide('hospital', 3)"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="case-details">
                                    <div class="case-highlights">
                                        <h3>监测内容</h3>
                                        <ul class="highlight-list">
                                            <li><i class="fas fa-search"></i>蚊虫种类、数量、性别智能识别</li>
                                            <li><i class="fas fa-chart-line"></i>密度分析与蚊种构成统计</li>
                                            <li><i class="fas fa-clock"></i>日活动节律规律追踪</li>
                                            <li><i class="fas fa-map-marked-alt"></i>空间分布模式分析</li>
                                            <li><i class="fas fa-bell"></i>密度变化预警预测系统</li>
                                        </ul>
                                    </div>

                                    <div class="case-results">
                                        <h3>项目成果</h3>
                                        <p>
                                            我们针对不同生境进行了蚊虫成蚊监测，成功识别出白纹伊蚊、致倦库蚊等6种主要蚊虫种类。通过密度分析和空间分布数据，为医院制定精准的病媒防控策略提供了科学依据，
                                            实现了病媒生物的智能化监测管理。
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 案例2：街道办监测项目 -->
                <div class="case-item">
                    <div class="case-content">
                        <div class="case-header">
                            <div class="case-title-row">
                                <h2 class="case-title">街道办虫媒数字监测项目</h2>
                                <div class="case-category">政府机构</div>
                            </div>
                        </div>
                        <div class="case-row">
                            <div class="case-overview">
                                <h3>项目概述</h3>
                                <p>
                                    本项目对街道办辖区内各类生境开展精准的病媒生物监测，
                                    系统识别主要蚊虫种类、统计数量密度、分析性别比例，
                                    建立区域专属的病媒生物数据库，为科学防控提供决策依据。
                                </p>
                            </div>

                            <div class="case-content-grid">
                                <div class="case-image">
                                    <div class="image-carousel" data-carousel="street">
                                        <div class="carousel-container">
                                            <div class="carousel-slide active">
                                                <img src="assets/land_case/street_office/1.jpg" alt="街道办监测项目" class="project-image">
                                            </div>
                                            <div class="carousel-slide">
                                                <img src="assets/land_case/street_office/2.jpg" alt="街道办监测项目" class="project-image">
                                            </div>
                                            <div class="carousel-slide">
                                                <img src="assets/land_case/street_office/3.jpg" alt="街道办监测项目" class="project-image">
                                            </div>
                                        </div>
                                        <div class="carousel-dots">
                                            <span class="dot active" onclick="currentSlide('street', 1)"></span>
                                            <span class="dot" onclick="currentSlide('street', 2)"></span>
                                            <span class="dot" onclick="currentSlide('street', 3)"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="case-details">
                                    <div class="case-highlights">
                                        <h3>监测内容</h3>
                                        <ul class="highlight-list">
                                            <li><i class="fas fa-microscope"></i>AI智能种类识别与计数</li>
                                            <li><i class="fas fa-chart-bar"></i>密度统计与蚊种构成分析</li>
                                            <li><i class="fas fa-clock"></i>24小时活动节律监测</li>
                                            <li><i class="fas fa-map-marked-alt"></i>网格化空间分布追踪</li>
                                            <li><i class="fas fa-bell"></i>密度变化预警预测</li>
                                        </ul>
                                    </div>

                                    <div class="case-results">
                                        <h3>监测成果</h3>
                                        <p>
                                            我们针对不同生境进行了蚊虫成蚊监测，识别出白纹伊蚊、三带喙库蚊等主要蚊种。
                                            通过日活动节律和空间分布分析，掌握了不同区域的蚊虫密度变化规律，
                                            为街道办制定针对性防控措施提供了精准的数据支撑。
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 案例3：**山生态监测 -->
                <div class="case-item">
                    <div class="case-content">
                        <div class="case-header">
                            <div class="case-title-row">
                                <h2 class="case-title">**山虫媒数字监测项目</h2>
                                <div class="case-category">生态环境</div>
                            </div>
                        </div>
                        <div class="case-row">
                            <div class="case-overview">
                                <h3>项目概述</h3>
                                <p>
                                    本项目针对**山山地生态环境开展病媒生物智能监测，
                                    系统识别山林、水体、步道等不同生境中的蚊虫种类、数量、性别分布，
                                    建立生态旅游区专属的病媒监测数据库。
                                </p>
                            </div>

                            <div class="case-content-grid">
                                <div class="case-image">
                                    <div class="image-carousel" data-carousel="mountain">
                                        <div class="carousel-container">
                                            <div class="carousel-slide active">
                                                <img src="assets/land_case/tiezai_mountain/1.jpg" alt="**山监测项目" class="project-image">
                                            </div>
                                            <div class="carousel-slide">
                                                <img src="assets/land_case/tiezai_mountain/2.jpg" alt="**山监测项目" class="project-image">
                                            </div>
                                            <div class="carousel-slide">
                                                <img src="assets/land_case/tiezai_mountain/3.jpg" alt="**山监测项目" class="project-image">
                                            </div>
                                        </div>
                                        <div class="carousel-dots">
                                            <span class="dot active" onclick="currentSlide('mountain', 1)"></span>
                                            <span class="dot" onclick="currentSlide('mountain', 2)"></span>
                                            <span class="dot" onclick="currentSlide('mountain', 3)"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="case-details">
                                    <div class="case-highlights">
                                        <h3>监测维度</h3>
                                        <ul class="highlight-list">
                                            <li><i class="fas fa-search"></i>多生境蚊虫种类识别</li>
                                            <li><i class="fas fa-chart-line"></i>密度分布与构成分析</li>
                                            <li><i class="fas fa-clock"></i>日夜活动节律监测</li>
                                            <li><i class="fas fa-map-marked-alt"></i>空间分布热力图绘制</li>
                                            <li><i class="fas fa-calendar-alt"></i>季节变化趋势预测</li>
                                        </ul>
                                    </div>

                                    <div class="case-results">
                                        <h3>监测发现</h3>
                                        <p>
                                            项目识别出白纹伊蚊、骚扰阿蚊等5种主要蚊虫种类，掌握了不同海拔和生境条件下的蚊虫密度分布规律。
                                            通过活动节律分析，发现山区蚊虫具有明显的昼夜活动差异，
                                            为生态旅游区的游客安全和环境管理提供了科学的监测数据。
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 案例4：建筑工地监测 -->
                <div class="case-item">
                    <div class="case-content">
                        <div class="case-header">
                            <div class="case-title-row">
                                <h2 class="case-title">中建局工地蚊媒监测项目</h2>
                                <div class="case-category">建筑工程</div>
                            </div>
                        </div>
                        <div class="case-row">
                            <div class="case-overview">
                                <h3>项目概述</h3>
                                <p>
                                    本项目针对建筑工地复杂环境开展病媒生物监测，
                                    系统识别施工区、生活区、材料区等不同区域的蚊虫种类、数量及性别构成，
                                    建立工地专属的病媒监测体系，保障施工人员健康安全。
                                </p>
                            </div>

                            <div class="case-content-grid">
                                <div class="case-image">
                                    <div class="image-carousel" data-carousel="construction">
                                        <div class="carousel-container">
                                            <div class="carousel-slide active">
                                                <img src="assets/land_case/building_site/1.jpg" alt="工地监测项目" class="project-image">
                                            </div>
                                            <div class="carousel-slide">
                                                <img src="assets/land_case/building_site/2.jpg" alt="工地监测项目" class="project-image">
                                            </div>
                                            <div class="carousel-slide">
                                                <img src="assets/land_case/building_site/3.jpg" alt="工地监测项目" class="project-image">
                                            </div>
                                        </div>
                                        <div class="carousel-dots">
                                            <span class="dot active" onclick="currentSlide('construction', 1)"></span>
                                            <span class="dot" onclick="currentSlide('construction', 2)"></span>
                                            <span class="dot" onclick="currentSlide('construction', 3)"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="case-details">
                                    <div class="case-highlights">
                                        <h3>监测内容</h3>
                                        <ul class="highlight-list">
                                            <li><i class="fas fa-search"></i>多区域蚊虫种类识别</li>
                                            <li><i class="fas fa-chart-bar"></i>密度统计与构成分析</li>
                                            <li><i class="fas fa-clock"></i>施工期活动节律追踪</li>
                                            <li><i class="fas fa-map-marked-alt"></i>工地空间分布监测</li>
                                            <li><i class="fas fa-bell"></i>密度异常预警系统</li>
                                        </ul>
                                    </div>

                                    <div class="case-results">
                                        <h3>监测成果</h3>
                                        <p>
                                            项目识别出白纹伊蚊、致倦库蚊等4种主要蚊虫种类，掌握了不同施工阶段的蚊虫密度变化规律。
                                            通过空间分布分析，发现临时积水区域蚊虫密度显著较高，
                                            为工地环境管理和蚊虫防控提供了精准的数据指导。
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 案例5：城市公园监测 -->
                <div class="case-item">
                    <div class="case-content">
                        <div class="case-header">
                            <div class="case-title-row">
                                <h2 class="case-title">城市公园蚊虫生态监测项目</h2>
                                <div class="case-category">城市公园</div>
                            </div>
                        </div>
                        <div class="case-row">
                            <div class="case-overview">
                                <h3>项目概述</h3>
                                <p>
                                    本项目对城市公园绿地生态系统开展病媒生物智能监测，
                                    系统识别水体景观、绿化带、游客活动区等不同生境的蚊虫种类、数量、性别分布，
                                    建立公园专属的病媒监测数据体系。
                                </p>
                            </div>

                            <div class="case-content-grid">
                                <div class="case-image">
                                    <div class="image-carousel" data-carousel="park">
                                        <div class="carousel-container">
                                            <div class="carousel-slide active">
                                                <img src="assets/land_case/park/1.jpg" alt="公园监测项目" class="project-image">
                                            </div>
                                            <div class="carousel-slide">
                                                <img src="assets/land_case/park/2.jpg" alt="公园监测项目" class="project-image">
                                            </div>
                                            <div class="carousel-slide">
                                                <img src="assets/land_case/park/3.jpg" alt="公园监测项目" class="project-image">
                                            </div>
                                        </div>
                                        <div class="carousel-dots">
                                            <span class="dot active" onclick="currentSlide('park', 1)"></span>
                                            <span class="dot" onclick="currentSlide('park', 2)"></span>
                                            <span class="dot" onclick="currentSlide('park', 3)"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="case-details">
                                    <div class="case-highlights">
                                        <h3>监测内容</h3>
                                        <ul class="highlight-list">
                                            <li><i class="fas fa-search"></i>多生境蚊虫种类智能识别</li>
                                            <li><i class="fas fa-chart-line"></i>密度分布与蚊种构成分析</li>
                                            <li><i class="fas fa-clock"></i>季节性活动节律监测</li>
                                            <li><i class="fas fa-map-marked-alt"></i>景观区域空间分布图</li>
                                            <li><i class="fas fa-bell"></i>密度变化预警系统</li>
                                        </ul>
                                    </div>

                                    <div class="case-results">
                                        <h3>监测成果</h3>
                                        <p>
                                            项目识别出白纹伊蚊、三带喙库蚊等7种主要蚊虫种类，发现景观水体区域蚊虫密度最高。
                                            通过活动节律分析，掌握了公园蚊虫的季节性变化规律和日夜活动特征，
                                            为公园管理部门制定科学的病媒防控策略提供了数据支撑。
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 案例6：垃圾站监测 -->
                <div class="case-item">
                    <div class="case-content">
                        <div class="case-header">
                            <div class="case-title-row">
                                <h2 class="case-title">垃圾中转站病媒防控监测项目</h2>
                                <div class="case-category">环卫设施</div>
                            </div>
                        </div>
                        <div class="case-row">
                            <div class="case-overview">
                                <h3>项目概述</h3>
                                <p>
                                    本项目对垃圾中转站环境开展病媒生物智能监测，
                                    系统识别垃圾收集、转运、处理各环节的蚊蝇种类、数量及性别构成，
                                    建立中转站专属的病媒监测数据库，为环境管理提供科学依据。
                                </p>
                            </div>

                            <div class="case-content-grid">
                                <div class="case-image">
                                    <div class="image-carousel" data-carousel="waste">
                                        <div class="carousel-container">
                                            <div class="carousel-slide active">
                                                <img src="assets/land_case/dump/1.jpg" alt="垃圾站监测项目" class="project-image">
                                            </div>
                                            <div class="carousel-slide">
                                                <img src="assets/land_case/dump/2.jpg" alt="垃圾站监测项目" class="project-image">
                                            </div>
                                            <div class="carousel-slide">
                                                <img src="assets/land_case/dump/3.jpg" alt="垃圾站监测项目" class="project-image">
                                            </div>
                                        </div>
                                        <div class="carousel-dots">
                                            <span class="dot active" onclick="currentSlide('waste', 1)"></span>
                                            <span class="dot" onclick="currentSlide('waste', 2)"></span>
                                            <span class="dot" onclick="currentSlide('waste', 3)"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="case-details">
                                    <div class="case-highlights">
                                        <h3>监测范围</h3>
                                        <ul class="highlight-list">
                                            <li><i class="fas fa-search"></i>蚊蝇种类精准识别</li>
                                            <li><i class="fas fa-chart-bar"></i>密度统计与构成分析</li>
                                            <li><i class="fas fa-clock"></i>24小时活动节律监测</li>
                                            <li><i class="fas fa-map-marked-alt"></i>站内空间分布追踪</li>
                                            <li><i class="fas fa-bell"></i>密度异常预警系统</li>
                                        </ul>
                                    </div>

                                    <div class="case-results">
                                        <h3>监测成果</h3>
                                        <p>
                                            项目识别出家蝇、果蝇、致倦库蚊等6种主要病媒种类，掌握了垃圾处理不同环节的蚊蝇密度分布规律。
                                            通过活动节律分析，发现病媒活动高峰时段，
                                            为中转站制定精准的环境管理和防控措施提供了有力的数据支撑。
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <div id="footer-container"></div>
    <!--footer js render-->

    <script src="js/common.js"></script>
    <script src="js/footer.js"></script>
    <script>
        // 注册ScrollTrigger插件
        gsap.registerPlugin(ScrollTrigger);

        // 案例页面脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 页面整体滑动动画初始化
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // 添加动画类
                        entry.target.classList.add('animate-in');
                    } else {
                        // 元素离开视口时移除动画类（支持反向动画）
                        entry.target.classList.remove('animate-in');
                    }
                });
            }, {
                threshold: 0.1, // 当元素10%可见时触发
                rootMargin: '0px 0px -50px 0px' // 提前50px触发动画
            });

            // 为所有需要动画的元素添加观察
            const animatedElements = document.querySelectorAll('.page-section, .slide-in-left, .slide-in-right, .slide-in-scale');
            animatedElements.forEach(el => {
                observer.observe(el);
            });

            // 页面加载动画
            gsap.fromTo('.cases-title',
                { x: -50, opacity: 0 },
                { x: 0, opacity: 1, duration: 0.8, ease: "power2.out" }
            );

            gsap.fromTo('.cases-info',
                { x: 50, opacity: 0 },
                { x: 0, opacity: 1, duration: 0.8, delay: 0.2, ease: "power2.out" }
            );

            // 图片轮播功能
            let carouselIntervals = {};

            // 轮播控制函数（仅用于自动播放）
            function changeSlide(carouselId, direction) {
                const carousel = document.querySelector(`[data-carousel="${carouselId}"]`);
                const slides = carousel.querySelectorAll('.carousel-slide');
                const dots = carousel.querySelectorAll('.dot');

                let currentSlide = Array.from(slides).findIndex(slide => slide.classList.contains('active'));

                // 移除当前活动状态
                slides[currentSlide].classList.remove('active');
                dots[currentSlide].classList.remove('active');

                // 计算新的幻灯片索引
                currentSlide += direction;
                if (currentSlide >= slides.length) currentSlide = 0;
                if (currentSlide < 0) currentSlide = slides.length - 1;

                // 设置新的活动状态
                slides[currentSlide].classList.add('active');
                dots[currentSlide].classList.add('active');
            }

            // 直接跳转到指定幻灯片
            window.currentSlide = function(carouselId, slideIndex) {
                const carousel = document.querySelector(`[data-carousel="${carouselId}"]`);
                const slides = carousel.querySelectorAll('.carousel-slide');
                const dots = carousel.querySelectorAll('.dot');

                // 移除所有活动状态
                slides.forEach(slide => slide.classList.remove('active'));
                dots.forEach(dot => dot.classList.remove('active'));

                // 设置指定幻灯片为活动状态
                slides[slideIndex - 1].classList.add('active');
                dots[slideIndex - 1].classList.add('active');
            }

            // 自动播放功能
            function startAutoPlay(carouselId, interval = 4000) {
                if (carouselIntervals[carouselId]) {
                    clearInterval(carouselIntervals[carouselId]);
                }

                carouselIntervals[carouselId] = setInterval(() => {
                    changeSlide(carouselId, 1);
                }, interval);
            }

            // 停止自动播放
            function stopAutoPlay(carouselId) {
                if (carouselIntervals[carouselId]) {
                    clearInterval(carouselIntervals[carouselId]);
                }
            }

            // 初始化所有轮播
            function initCarousels() {
                const carousels = document.querySelectorAll('.image-carousel');

                carousels.forEach(carousel => {
                    const carouselId = carousel.getAttribute('data-carousel');

                    // 启动自动播放
                    startAutoPlay(carouselId);

                    // 鼠标悬停时停止自动播放
                    carousel.addEventListener('mouseenter', () => {
                        stopAutoPlay(carouselId);
                    });

                    // 鼠标离开时恢复自动播放
                    carousel.addEventListener('mouseleave', () => {
                        startAutoPlay(carouselId);
                    });
                });
            }

            // 初始化轮播
            initCarousels();



            // 案例卡片动画
            gsap.fromTo('.case-item',
                { y: 50, opacity: 0 },
                {
                    y: 0,
                    opacity: 1,
                    duration: 0.8,
                    stagger: 0.3,
                    delay: 0.5,
                    ease: "power2.out",
                    scrollTrigger: {
                        trigger: '.cases-list',
                        start: 'top 80%',
                        toggleActions: 'play none none reverse'
                    }
                }
            );


        });
    </script>
</body>
</html>
