import imgCustomer1 from '@/assets/customer/img_customer_1.webp';
import imgCustomer2 from '@/assets/customer/img_customer_2.jpg';
import imgCustomer3 from '@/assets/customer/img_customer_3.jpg';
import imgCustomer4 from '@/assets/customer/img_customer_4.jpg';
import { PrimaryText } from '@/components/PrimaryText';
import { SectionTitle } from '@/components/SectionTips';
import { CustomerListItem } from '../../components/ListItem';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-14 00:11:14
 * @LastEditTime: 2024-12-18 12:07:39
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export default function Customer() {
  return (
    <div className="flex flex-col w-full">
      <header
        className="w-full h-[6.8rem] px-[2rem] flex flex-col justify-center"
        style={{
          backgroundImage: `url(${imgCustomer1})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
        }}
      >
        <p className="font-medium text-[.64rem] mb-[.26rem] text-[#000000]">
          成功
          <PrimaryText content="案例" />
        </p>
        <p className="font-normal text-[.16rem] text-[#000000]">
          银河星尘已经累计帮助多个客户低成本、高效率处理病媒生物监测问题
        </p>
      </header>
      <section className="w-full h-[10.23rem]">
        <SectionTitle
          className="mt-[1.30rem] mb-[.7rem]"
          classNames={{
            title: ' text-black',
          }}
          iconColor="#0067FF"
          title="**医院虫媒密度、种类数字监测项目"
        />
        <div className="flex flex-row items-center justify-center gap-[1.84rem] ">
          {/* list */}
          <div className="flex flex-col w-[6.8rem] h-[5.2rem] gap-[.4rem]">
            <CustomerListItem
              title="项目概况"
              content={[
                <p key={1}>项目名称: **医院虫媒密度、种类数字监测项目</p>,
                <p key={2}>项目地点: 深圳市宝安区**医院</p>,
              ]}
            />
            <CustomerListItem
              title="项目目标"
              content={[
                <p key={1}>
                  掌握了解宝安**医院和周边的主要蚊虫种类、密度及蚊虫携带引发急病源、并建立医院专属病媒生物基础数据库
                </p>,
                <p key={2}>
                  通过蚊虫分析报告、数据标本进行收集和分析，为相应疾病预测、防控提供有力的传播科学依据
                </p>,
              ]}
            />
            <CustomerListItem
              title="监测范围"
              content="蚊虫数字监测服务范围为**医院，分别综合楼旁边、医院3号楼边、后靠山、医院5号楼后靠山、医院1号楼后、医院2号楼后多处设置若干监测点"
            />
          </div>
          <div className="flex w-[7.2rem] h-[6rem] overflow-hidden">
            <img src={imgCustomer2} className="w-[7.2rem] object-cover " />
          </div>
        </div>
      </section>
      <section className="bg-[#EEF5FD] w-full h-[10.13rem]">
        <SectionTitle
          className="mt-[1.30rem] mb-[.7rem]"
          classNames={{
            title: ' text-black',
          }}
          iconColor="#0067FF"
          title="**街道办虫媒数字监测项目"
        />
        <div className="flex flex-row items-center justify-center gap-[1.84rem] ">
          <div className="flex w-[7.2rem] h-[6rem] overflow-hidden">
            <img src={imgCustomer3} className="w-[7.2rem] object-cover" />
          </div>
          {/* list */}
          <div className="flex flex-col w-[6.8rem] h-[5.2rem] gap-[.4rem]">
            <CustomerListItem
              title="项目概况"
              content={[
                <p key={1}>项目名称: **街道办虫媒数字监测项目</p>,
                <p key={2}>项目地点: 深圳市**街道办</p>,
              ]}
            />
            <CustomerListItem
              title="项目目标"
              content={[
                <p key={1}>
                  本项目致力于精确识别并深入了解街道办及其周边区域的主要虫媒生物种类、其分布的密集度以及它们所携带的病原体，建立一个街道专属的病媒生物基础数据库。
                </p>,
                <p key={2}>
                  通过搜集和分析虫媒分析报告以及相关的数据标本，本项目将为街道办辖区的疾病预测和防控工作提供坚实的科学基础
                </p>,
              ]}
            />
            <CustomerListItem
              title="监测范围"
              content="蚊虫数字监测服务范围为**街道全区域，并设置若干监测点"
            />
          </div>
        </div>
      </section>
      <section className="bg-white w-full h-[10.23rem]">
        <SectionTitle
          className="mt-[1.30rem] mb-[.7rem]"
          classNames={{
            title: ' text-black',
          }}
          iconColor="#0067FF"
          title="**公共卫生服务中心虫媒数字监测设备采购项目"
        />
        <div className="flex flex-row items-center justify-center gap-[1.84rem] ">
          {/* list */}
          <div className="flex flex-col w-[6.8rem] h-[5.2rem] gap-[.4rem]">
            <CustomerListItem
              title="项目概况"
              content={[
                <p key={1}>项目名称: **公共卫生服务中心虫媒数字监测设备采购项目</p>,
                <p key={2}>项目地点: 深圳市**公共卫生服务中心</p>,
              ]}
            />
            <CustomerListItem
              title="项目目标"
              content="通过采购先进的蚊虫监测设备，更准确地监测和评估蚊虫种群的分布和密度，从而采取有效的控制措施，减少蚊虫传播疾病的风险"
            />
            <CustomerListItem
              title="监测范围"
              content="项目采购了蚊虫监测、太阳能供电套装和施工安装三类设备若干套"
            />
          </div>
          <div className="flex w-[7.2rem] h-[6rem] overflow-hidden">
            <img src={imgCustomer4} className="w-[7.2rem] object-cover" />
          </div>
        </div>
      </section>
    </div>
  );
}
