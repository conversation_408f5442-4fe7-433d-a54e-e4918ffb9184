import logoName from '@/assets/img_logo_name.png';
import logo from '@/assets/logo.svg';
import { commonStyles } from '@/constants/styles';
import { useNavStore } from '@/store';
import { history, Outlet, useLocation } from '@umijs/max';
import { Button, ConfigProvider, Dropdown, MenuProps } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-10 16:57:39
 * @LastEditTime: 2025-01-06 18:39:15
 * @LastEditors: L.Zhang
 * @Description:
 */
export default function BasicLayout() {
  const { tab, dropMenusKeys, updateDropMenusKeys, updateTab } = useNavStore();
  const navRef = useRef<HTMLElement>(null);

  const [showHerader, setShowHerader] = useState(false);
  const handlerScroll = useCallback((e: any) => {
    const offsetHeight = e.srcElement.documentElement.scrollTop;
    const bannerHeight = navRef.current?.clientHeight;

    if (bannerHeight && offsetHeight >= bannerHeight) {
      setShowHerader(true);
    } else {
      setShowHerader(false);
    }
  }, []);
  const location = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]); // 监听路径名的变化

  useEffect(() => {
    window.addEventListener('scroll', handlerScroll);
    return () => window.removeEventListener('scroll', handlerScroll);
  }, [handlerScroll]);

  const style = {
    navTransparent:
      'px-[2rem] bg-transparent h-[0.7rem] w-full flex items-center fixed transition-all',
    navWhite: 'px-[2rem] bg-white h-[0.7rem] w-full flex items-center fixed transition-all',
    activeNav: `bg-white/80 backdrop-blur-[35px] shadow-[0px_0px_15px_0px_#000000]`,
    navLink: `text-[0.16rem] font-normal text-[#555555]`,
    activeNavLink: `text-[#0360D9] text-[0.16rem] font-medium`,
    footerLink: `text-[#666666] font-normal text-[.16rem] ${commonStyles.hover} hover:text-[#0360D9]`,
    footerHeader: 'text-[#222222] font-semibold text-[.16rem]',
    footerHighlight:
      'relative border-t-[1px] border-[#F0F0F0] shadow-[0px_-.15rem_.4rem_0rem_#C6D4F2]',
  };

  const dropItems: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <a
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => {
            updateDropMenusKeys(['1']);
            history.push('/product/solution');
          }}
        >
          解决方案
        </a>
      ),
    },
    {
      key: '2',
      label: (
        <a
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => {
            updateDropMenusKeys(['2']);
            history.push('/product/aiaas');
          }}
        >
          星尘AS平台
        </a>
      ),
    },
    {
      key: '3',
      label: (
        <a
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => {
            updateDropMenusKeys(['3']);
            history.push('/product/iot');
          }}
        >
          物联网硬件
        </a>
      ),
    },
    {
      key: '4',
      label: (
        <a
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => {
            updateDropMenusKeys(['4']);
            history.push('/product/llm');
          }}
        >
          大模型算法
        </a>
      ),
    },
  ];
  const dropAboutItems: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <a
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => {
            updateDropMenusKeys(['1']);
            history.push('/about/intro');
          }}
        >
          公司介绍
        </a>
      ),
    },
    {
      key: '2',
      label: (
        <a
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => {
            updateDropMenusKeys(['2']);
            history.push('/about/act');
          }}
        >
          市场活动
        </a>
      ),
    },
  ];
  const navItems = [
    {
      key: '1',
      label: '首页',
      link: '/home',
    },
    {
      key: '2',
      label: (
        <Dropdown
          placement="bottom"
          menu={{ items: dropItems, selectable: true, selectedKeys: dropMenusKeys }}
        >
          <a onClick={(e) => e.preventDefault()}>产品服务</a>
        </Dropdown>
      ),
      link: '/product',
      disable: true,
    },
    {
      key: '3',
      label: '客户案例',
      link: '/customer',
    },
    {
      key: '4',
      label: (
        <Dropdown
          placement="bottom"
          menu={{ items: dropAboutItems, selectable: true, selectedKeys: dropMenusKeys }}
        >
          <a onClick={(e) => e.preventDefault()}>关于我们</a>
        </Dropdown>
      ),
      link: '/about',
      disable: true,
    },
  ];

  const goHome = () => {
    updateTab('/home');
    updateDropMenusKeys([]);
    history.push('/home');
  };

  const goProductSolution = () => {
    updateTab('/product');
    updateDropMenusKeys(['1']);
    history.push('/product/solution');
  };
  const goProductAiaas = () => {
    updateTab('/product');
    updateDropMenusKeys(['2']);
    history.push('/product/aiaas');
  };
  const goProductIot = () => {
    updateTab('/product');
    updateDropMenusKeys(['3']);
    history.push('/product/iot');
  };
  const goProductLLM = () => {
    updateTab('/product');
    updateDropMenusKeys(['4']);
    history.push('/product/llm');
  };
  const goXxc = () => {
    history.push('/product/xxc');
  };
  const goCustomer = () => {
    updateTab('/customer');
    history.push('/customer');
  };
  const goAbout = () => {
    updateTab('/about/intro');
    history.push('/about/intro');
  };
  const goAboutAct = () => {
    updateTab('/about/act');
    history.push('/about/act');
  };

  return (
    <ConfigProvider>
      <div className="w-full h-full relative">
        <nav
          ref={navRef}
          className={`z-[100] ${style.navTransparent} ${
            showHerader ? style.activeNav : ''
          } bg-white`}
        >
          <div className="cursor-pointer flex flex-row gap-[0.1rem] items-center" onClick={goHome}>
            <img src={logo} alt="logo" className="h-[0.32rem]" />
            <img src={logoName} alt="" className="h-[0.22rem]" />
          </div>
          <div className="absolute left-[50%] translate-x-[-50%] flex flex-row items-center gap-[0.5rem]">
            {navItems.map((item) => {
              return (
                <Button
                  key={item.key}
                  type="link"
                  onClick={() => {
                    updateTab(item.link);
                    if (item.disable) {
                      return;
                    }
                    history.push(item.link);
                    updateDropMenusKeys([]);
                  }}
                >
                  <div className={`${tab === item.link ? style.activeNavLink : style.navLink}`}>
                    {item.label}
                  </div>
                </Button>
              );
            })}
          </div>
        </nav>
        <Outlet />
        {/* footer  */}
        <section
          className={`bg-white w-full min-h-[4.2rem] px-[2rem] flex flex-col border-t-[1px] border-[#F0F0F0] relative shadow-[0rem_-.15rem_.4rem_0rem_#C6D4F2]`}
        >
          <div className="flex pt-[.5rem] min-h-[3.2rem]">
            <div className="flex flex-col mr-auto">
              <div className="flex flex-row gap-[0.1rem] items-center mb-[.3rem]">
                <img src={logo} alt="logo" className="w-[0.44rem] h-[0.4rem]" />
                <img src={logoName} alt="" className="w-[1.14rem] h-[0.2668rem]" />
              </div>
              <div className="text-[.18rem] text-[#555555] font-normal">
                <p>为实现病媒可控而创新!</p>
                <p>通过创新技术帮助社区消除媒介传播疾病!</p>
              </div>
            </div>
            {/* foot nav */}
            <div className="flex gap-[1rem]">
              <div className="flex flex-col gap-[.2rem]">
                <p className={style.footerHeader}>产品服务</p>
                <p className={`${style.footerLink}`} onClick={goProductSolution}>
                  解决方案
                </p>
                <p className={`${style.footerLink}`} onClick={goProductAiaas}>
                  星尘AS平台
                </p>
                <p className={`${style.footerLink}`} onClick={goProductIot}>
                  物联网硬件
                </p>
                <p className={`${style.footerLink}`} onClick={goProductLLM}>
                  大模型算法
                </p>
                <p className={`${style.footerLink}`} onClick={goXxc}>
                  病媒生物专家
                </p>
              </div>
              <div className="flex flex-col gap-[.2rem]">
                <p className={style.footerHeader}>探索我们</p>
                <p className={`${style.footerLink}`} onClick={goCustomer}>
                  客户案例
                </p>
                <p className={`${style.footerLink} `} onClick={goXxc}>
                  星小尘
                </p>
              </div>
              <div className="flex flex-col gap-[.2rem]">
                <p className={style.footerHeader}>支持服务</p>
                <p className={`${style.footerLink}`}>联系我们</p>
              </div>
              <div className="flex flex-col gap-[.2rem]">
                <p className={style.footerHeader}>关于我们</p>
                <p className={`${style.footerLink}`} onClick={goAbout}>
                  企业介绍
                </p>
                <p className={`${style.footerLink}`} onClick={goAboutAct}>
                  企业活动
                </p>
              </div>
            </div>
            {/* foot qr */}
            <div className="flex flex-col items-center ml-[1.27rem]">
              {/* <img src={imgQrAboutUs} className="w-[1rem] h-[1rem]" />
              <p className="text-[.14rem] font-normal text-[#555555] mt-[.06rem]">关注我们</p> */}
            </div>
          </div>
          <div className="flex justify-between text-[.14rem] h-[1rem] text-[#555555] border-t-[0.5px] border-[#555555]]">
            <span className="mt-[.4rem]">©2024-2025 深圳银河星尘科技有限公司</span>
            <span
              className="mt-[.4rem] cursor-pointer hover:text-[#0360D9]"
              onClick={() => {
                window.open('https://beian.miit.gov.cn');
              }}
            >
              粤ICP备2024351884号-1
            </span>
          </div>
        </section>
        {/* 在线客服 */}
        <div
          onClick={() => history.push('/product/xxc')}
          className="bg-white z-[100] fixed top-1/2 right-[.56rem] flex flex-col items-center justify-center gap-[.1rem] px-[.1rem] py-[.16rem] w-[.52rem] rounded-[.6rem] border-[#69BEF7] border hover:cursor-pointer"
        >
          <img src={logo} alt="logo" className="w-[0.30rem] h-[0.3rem]" />
          <div className={`text-[.14rem] text-[#333333] font-normal ${commonStyles.hover}`}>
            <p className="font-normal">在</p>
            <p className="font-normal">线</p>
            <p className="font-normal">咨</p>
            <p className="font-normal">询</p>
          </div>
        </div>
      </div>
    </ConfigProvider>
  );
}
