/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-17 19:48:31
 * @LastEditTime: 2025-01-09 13:35:03
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { isBaseImg } from '@/utils/file.util';
import { Image, Spin } from 'antd';
import moment from 'moment';
import {
  JSXElementConstructor,
  Key,
  memo,
  ReactElement,
  ReactNode,
  useCallback,
  useMemo,
} from 'react';
import Markdown from 'react-markdown';
import imgAvatar from './assets/img_avatar.png';

interface IProps {
  messageList: any[];
  uniqueId: string;
}

// TODO 每条消息可能有闪烁问题
export const MessageBox = memo((props: IProps) => {
  const { messageList, uniqueId } = props;
  const currentTime = useMemo(() => moment().format('今天HH:mm'), []);

  const isBase64Img = useCallback((str: string) => {
    const isbase64 = isBaseImg(str);
    return isbase64 ? (
      <Image src={str} width={140} height={140} style={{ objectFit: 'cover' }} />
    ) : (
      <Markdown
        components={{
          p: 'div',
          img({ src }) {
            return <Image src={src} style={{ objectFit: 'cover' }} />;
          },
        }}
      >
        {str}
      </Markdown>
    );
  }, []);

  return (
    <section
      id={'msg' + uniqueId}
      className={`p-[.2rem] flex-1 flex flex-col custom-scrollbar overflow-y-auto scroll-smooth text-white text-[.2rem]`}
    >
      <p className="w-full text-center mt-[.2rem]">{currentTime}</p>
      {messageList?.map(
        (
          message: {
            person: string;
            nickname:
              | string
              | number
              | boolean
              | ReactElement<any, string | JSXElementConstructor<any>>
              | Iterable<ReactNode>
              | null
              | undefined;
            text: string;
          },
          index: Key | null | undefined,
        ) => {
          return (
            <div key={index} className="relative flex flex-col gap-[10px] mb-[16px]">
              {message.person === 'secondary' ? (
                <div className="flex flex-col w-full">
                  {/* absolute top-[-35px] left-[0px]  */}
                  {/* 人物头像、名称 */}
                  <p className="mb-[.10rem]">{message.nickname}</p>
                  {/* 人物消息间隔 */}
                  <div className="flex gap-[.1rem] w-full">
                    <img src={imgAvatar} className="w-[.50rem] h-[.50rem]" />
                    <div
                      className={`fade-in bg-[#535354] p-[.1rem] w-max max-w-[50%] rounded-[8px] clear-both `}
                    >
                      {message.text === '...' ? <Spin /> : isBase64Img(message.text)}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col w-full ">
                  <p className={'ml-auto mb-[.10rem]'}>{message.nickname}</p>
                  <div className={'bg-[#535354] p-[.1rem] w-max max-w-[50%] rounded-[8px] ml-auto'}>
                    {message.text === '...' ? <Spin /> : isBase64Img(message.text)}
                  </div>
                </div>
              )}
            </div>
          );
        },
      )}
    </section>
  );
});
