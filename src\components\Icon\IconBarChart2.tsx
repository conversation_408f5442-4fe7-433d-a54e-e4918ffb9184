/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 12:14:35
 * @LastEditTime: 2024-12-18 15:59:35
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}

export const IconBarChart2 = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.5 17.5H5.16667C4.23325 17.5 3.76654 17.5 3.41002 17.3183C3.09641 17.1586 2.84144 16.9036 2.68166 16.59C2.5 16.2335 2.5 15.7668 2.5 14.8333V2.5M5.83333 12.0833V14.5833M9.58333 9.58333V14.5833M13.3333 7.08333V14.5833M17.0833 4.58333V14.5833"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
