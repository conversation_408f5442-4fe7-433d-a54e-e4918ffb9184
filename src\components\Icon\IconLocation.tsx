/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 01:41:43
 * @LastEditTime: 2024-12-18 16:05:19
 * @LastEditors: <PERSON><PERSON>
 * @Description: p
 */
interface IProps {
  className?: string;
}

export const IconLocation = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle opacity="0.2" cx="16" cy="16" r="16" fill="white" />
      <path
        d="M20.6433 9.97895L20.6023 9.93684C19.4561 8.71578 17.9415 8 16.2222 8C14.5029 8 12.9883 8.71578 11.8421 9.89473L11.8012 9.97895C10.6959 11.1579 10 12.8 10 14.6105C10 16.421 10.6959 18.0632 11.8012 19.2421L11.8421 19.2842L16.2222 24L20.6023 19.3263L20.6433 19.2842C21.7485 18.1053 22.4444 16.4632 22.4444 14.6105C22.4444 12.8 21.7485 11.1579 20.6433 9.97895ZM16.2222 16.5053C15.0351 16.5053 14.0526 15.4526 14.0526 14.1895C14.0526 13.6421 14.2164 13.179 14.5029 12.8C14.9123 12.2526 15.5263 11.8737 16.2222 11.8737C17.4094 11.8737 18.3918 12.9263 18.3918 14.1895C18.3918 15.4526 17.4094 16.5053 16.2222 16.5053Z"
        fill="white"
      />
    </svg>
  );
};
