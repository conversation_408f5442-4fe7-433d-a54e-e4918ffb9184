/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 01:41:43
 * @LastEditTime: 2024-12-18 18:12:39
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { IconArrowRight } from './Icon/IconArrowRight';
import { IconDroplets } from './Icon/IconDroplets';
import { IconLightning } from './Icon/IconLightning';
import { IconThermometer } from './Icon/IconThermometer';
import { IconWind } from './Icon/IconWind';

interface IProps {
  className?: string;
  url: string;
}
export const SectionProductList = (props: IProps) => {
  const { className, url } = props;
  const style = {
    link: 'flex h-[.4rem] font-normal text-[.14rem] text-[#666666] items-center',
    activeLink: 'flex h-[.4rem] font-medium text-[.18rem] text-[#000000] items-center',
  };

  return (
    <div className={`w-[15.2rem] h-[6rem] flex flex-row ${className}`}>
      <div className="rounded-tl-[4px] rounded-bl-[4px] w-[2.88rem] h-[6rem] bg-[#F3F8FF] p-[.48rem] gap-[.1rem]">
        <div className={`cursor-pointer ${style.activeLink}`}>
          蚊媒智能诱捕设备
          <span className="ml-[.08rem]">
            <IconArrowRight />
          </span>
        </div>
        {/* <div className={style.link}>蚊媒智能诱捕设备</div>
        <div className={style.link}>蚊媒智能诱捕设备</div>
        <div className={style.link}>蚊媒智能诱捕设备</div>
        <div className={style.link}>蚊媒智能诱捕设备</div> */}
      </div>
      <div className="w-[4.96rem] h-[6rem] bg-[#E9F3FF] flex flex-col justify-between p-[.48rem]">
        <p className="font-semibold text-[.36rem]">自研物联网 (IoT) 设备 -蚊媒智能诱捕设备</p>
        <div className="flex flex-1 flex-col  justify-end">
          <p className="flex items-center mb-[.18rem]">
            <span className="mr-[.06rem]">
              <IconLightning className="size-[.16rem]" />
            </span>
            <span className="text-[.14rem] font-medium">工作电压:</span>
            <span className="text-[.14rem] font-normal">（AC220V）</span>
          </p>
          <p className="flex items-center mb-[.18rem]">
            <span className="mr-[.06rem]">
              <IconWind className="size-[.16rem]" />
            </span>
            <span className="text-[.14rem] font-medium">工作电流: </span>
            <span className="text-[.14rem] font-normal">（&lt;=0.5A）</span>
          </p>
          <p className="flex items-center mb-[.18rem]">
            <span className="mr-[.06rem]">
              <IconThermometer className="size-[.16rem]" />
            </span>
            <span className="text-[.14rem] font-medium">工作温度: </span>
            <span className="text-[.14rem] font-normal">（&lt;70摄氏度）</span>
          </p>
          <p className="flex items-center mb-[.18rem]">
            <span className="mr-[.06rem]">
              <IconDroplets className="size-[.16rem]" />
            </span>
            <span className="text-[.14rem] font-medium">工作湿度: </span>
            <span className="text-[.14rem] font-normal">（&lt;85%RH）</span>
          </p>
        </div>
      </div>
      <div className="rounded-tr-[4px] rounded-br-[4px] w-[7.36rem] h-[6rem] bg-[#DCEBFF] flex flex-col justify-center items-center">
        <img src={url} className="w-[2.71rem] h-[4.6rem]" />
      </div>
    </div>
  );
};
