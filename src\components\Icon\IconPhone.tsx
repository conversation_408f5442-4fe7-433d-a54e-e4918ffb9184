/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 01:41:43
 * @LastEditTime: 2024-12-18 16:04:59
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}

export const IconPhone = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle opacity="0.2" cx="16" cy="16" r="16" fill="white" />
      <path
        d="M22.961 20.3147C22.6664 19.2779 20.5441 17.9598 19.3658 17.6822C18.8786 17.567 18.5425 17.6199 18.3405 17.8427C18.2612 17.9296 18.1838 18.0316 18.1026 18.1392C17.8363 18.4943 17.6381 18.7265 17.3133 18.7511C16.9357 18.7756 16.2049 18.5226 14.8416 17.1591C13.4934 15.8108 13.2442 15.0649 13.2744 14.6759C13.3027 14.3058 13.601 14.0829 13.8918 13.8695C13.9919 13.7959 14.0825 13.7279 14.1562 13.6599C14.4602 13.3861 14.3431 12.8819 14.3054 12.7176C14.058 11.645 12.7759 9.35434 11.6751 9.03898C11.3541 8.94644 11.0519 9.02009 10.8272 9.24481C9.97376 10.0984 8.8597 11.4486 9.01454 13.3294C9.15804 15.0687 10.3495 17.0364 12.6569 19.344C15.3665 22.0539 17.4228 23 19.0599 23C20.5988 23 21.7657 22.1653 22.7571 21.1739C22.9799 20.9492 23.0535 20.6433 22.961 20.3147Z"
        fill="white"
      />
    </svg>
  );
};
