html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

html {
  font-size: calc(100vw / 1920 * 100);
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

// 自定义滚动条样式
.custom-scrollbar {
  overflow-y: scroll; /* 垂直滚动条 */
  // scrollbar-color: #bcbcbc transparent; /* 滚动条的颜色 */
  scrollbar-width: thin; /* 滚动条的宽度 */
  &:hover {
    scrollbar-width: thin;
  }
}

/* 针对webkit内核浏览器的自定义样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 12px; /* 滚动条宽度 */
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent; /* 滚动条轨道的颜色 */
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  // background-color: #bcbcbc; /* 滚动条的颜色 */
  border-radius: 6px; /* 滚动条圆角 */
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  // background: #bcbcbc; /* 滚动条的悬停颜色 */
}
