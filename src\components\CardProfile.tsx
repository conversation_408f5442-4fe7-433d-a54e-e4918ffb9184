/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-15 18:29:57
 * @LastEditTime: 2024-12-16 10:44:07
 * @LastEditors: <PERSON><PERSON>
 * @Description: 合作机构卡片
 */

interface IProps {
  url: string;
  title: string;
  content: string;
  className?: string;
}

export const CardProfile = (props: IProps) => {
  const { url, title, content, className } = props;
  return (
    <div className={`flex flex-col w-[2.88rem] h-[3.16rem] bg-[#F3F8FF] p-[.3rem] ${className}`}>
      <div className="h-[.92rem]">
        <img src={url} className="w-[.92rem] h-[.92rem]" />
      </div>
      <p className="text-[.2rem] font-semibold text-[#222222] mt-[.16rem] mb-[.1rem]">{title}</p>
      <p className="text-[.16rem] font-normal text-[#555555]">{content}</p>
    </div>
  );
};
