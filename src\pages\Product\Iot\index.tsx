import imgIot1 from '@/assets/iot/img_iot_1.jpg';
import imgIot2 from '@/assets/iot/img_iot_2.jpg';
import imgIot3 from '@/assets/iot/img_iot_3.jpg';
import imgIotDevice1 from '@/assets/iot/img_iot_device_2.png';
import { CardProfile2 } from '@/components/CardProfile2';
import { IconBarChart } from '@/components/Icon/IconBarChart';
import { IconBezier } from '@/components/Icon/Iconbezier';
import { IconCamera } from '@/components/Icon/IconCamera';
import { IconHelp } from '@/components/Icon/IconHelp';
import { IconImage } from '@/components/Icon/IconImage';
import { IconLightBulb } from '@/components/Icon/IconLightBulb';
import { SectionProductList } from '@/components/SectionProductList';
import { SectionTitle } from '@/components/SectionTips';

export default function Iot() {
  return (
    <div className="flex flex-col w-full">
      <header
        className="w-full h-[7.26rem] flex flex-col justify-center px-[2rem]"
        style={{
          backgroundImage: `url(${imgIot1})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
        }}
      >
        <p className="font-medium text-[.64rem]">行业创新数字化的物联网监测设备</p>
        <p className="font-normal text-[.16rem] text-[#222222]">
          实时上传数据多类、高效的病媒生物引诱方式、数字边缘分析计算
        </p>
        <div
          className="absolute top-[2.58rem] right-[2.97rem] w-[2.29rem] h-[4.54rem]"
          style={{
            backgroundImage: `url(${imgIotDevice1})`,
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
          }}
        ></div>
      </header>
      <section className="relative bg-white w-full h-[10.96rem] px-[2rem]">
        <div
          className="absolute left-0 right-0 h-[5.7rem]"
          style={{
            backgroundImage: `url(${imgIot2})`,
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
          }}
        ></div>
        <div className="relative  text-[.48rem] font-medium text-white  mb-0 text-center mt-[1.08rem]">
          自研、创新、优于市场的智能设备
        </div>
        <SectionProductList url={imgIotDevice1} className="absolute top-[3.29rem]" />
      </section>
      <section
        className="w-full h-[10.79rem]"
        style={{
          backgroundImage: `url(${imgIot3})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
        }}
      >
        <SectionTitle className="mt-[1.31rem] mb-[.62rem]" title="设备优势" />
        <div className="flex flex-row gap-[.4rem] justify-center">
          <CardProfile2
            style={{
              background:
                'linear-gradient(120.62deg, rgba(0, 0, 0, 0.6) 12.03%, rgba(43, 102, 246, 0.6) 68.59%)',
            }}
            titleClassName="text-white text-[.24rem]"
            contentClassName="text-[#CCCCCC]"
            icon={<IconBarChart className="size-[.3rem]" />}
            title="数据无间断采集分析"
            content="7x24h室内外、全天候、主动式采集监测功能，确保无间断的数据收集与分析"
          />
          <CardProfile2
            style={{
              background:
                'linear-gradient(120.62deg, rgba(0, 0, 0, 0.6) 12.03%, rgba(73, 10, 191, 0.6) 68.59%)',
            }}
            titleClassName="text-white text-[.24rem]"
            contentClassName="text-[#CCCCCC]"
            icon={<IconImage className="size-[.3rem]" />}
            title="图片高效上传大模型平台"
            content="可编程逻辑控制器和4G/wifi网络，实现对通道内蚊鼠图片高效远程上传平台"
          />
          <CardProfile2
            style={{
              background:
                'linear-gradient(120.62deg, rgba(0, 0, 0, 0.6) 12.03%, rgba(40, 40, 40, 0.6) 68.59%)',
            }}
            titleClassName="text-white text-[.24rem]"
            contentClassName="text-[#CCCCCC]"
            icon={<IconBezier className="size-[.3rem]" />}
            title="全方位控制设备，数据安全无忧"
            content="支持就地+远程控制设备、读取参数、查看状态、上报异常，断电数据不损坏丢失"
          />
        </div>
        <div className="flex flex-row gap-[.4rem] mt-[.5rem] justify-center">
          <CardProfile2
            style={{
              background:
                'linear-gradient(120.62deg, rgba(0, 0, 0, 0.6) 12.03%, rgba(43, 102, 246, 0.6) 68.59%)',
            }}
            titleClassName="text-white text-[.24rem]"
            contentClassName="text-[#CCCCCC]"
            icon={<IconCamera className="size-[.3rem]" />}
            title="实时监测，高效灭杀蚊虫"
            content="防水密闭结构，支持实时监测蚊虫数量、种类、分布区域等，有效灭杀蚊虫"
          />
          <CardProfile2
            style={{
              background:
                'linear-gradient(120.62deg, rgba(0, 0, 0, 0.6) 12.03%, rgba(73, 10, 191, 0.6) 68.59%)',
            }}
            titleClassName="text-white text-[.24rem]"
            contentClassName="text-[#CCCCCC]"
            icon={<IconLightBulb className="size-[.3rem]" />}
            title="智慧诱捕，高效集虫"
            content="独特的模仿人体呼吸式捕诱技术，显著提高了集虫效率"
          />
          <CardProfile2
            style={{
              background:
                'linear-gradient(120.62deg, rgba(0, 0, 0, 0.6) 12.03%, rgba(40, 40, 40, 0.6) 68.59%)',
            }}
            titleClassName="text-white text-[.24rem]"
            contentClassName="text-[#CCCCCC]"
            icon={<IconHelp className="size-[.3rem]" />}
            title="卓越的适应性与稳定性"
            content="工业级像素，IP66防护级别，能够在-30至60摄氏度极端范围内正常工作"
          />
        </div>
      </section>
    </div>
  );
}
