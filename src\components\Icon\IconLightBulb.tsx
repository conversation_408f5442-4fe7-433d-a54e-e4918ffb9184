/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 11:01:50
 * @LastEditTime: 2024-12-16 11:02:11
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}
export const IconLightBulb = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="30"
      height="30"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.5 22.0732V25C12.5 26.3807 13.6193 27.5 15 27.5C16.3807 27.5 17.5 26.3807 17.5 25V22.0732M15 2.5V3.75M3.75 15H2.5M6.875 6.875L6.12488 6.12488M23.125 6.875L23.8753 6.12488M27.5 15H26.25M22.5 15C22.5 19.1421 19.1421 22.5 15 22.5C10.8579 22.5 7.5 19.1421 7.5 15C7.5 10.8579 10.8579 7.5 15 7.5C19.1421 7.5 22.5 10.8579 22.5 15Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
