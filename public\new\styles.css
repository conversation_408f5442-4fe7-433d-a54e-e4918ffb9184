/* 页面整体滑动动画初始状态 */
.page-section {
  opacity: 0;
  transform: translateY(60px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.page-section.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* 从左滑入效果 */
.slide-in-left {
  opacity: 0;
  transform: translateX(-80px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-in-left.animate-in {
  opacity: 1;
  transform: translateX(0);
}

/* 从右滑入效果 */
.slide-in-right {
  opacity: 0;
  transform: translateX(80px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-in-right.animate-in {
  opacity: 1;
  transform: translateX(0);
}

/* 缩放滑入效果 */
.slide-in-scale {
  opacity: 0;
  transform: scale(0.9) translateY(40px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-in-scale.animate-in {
  opacity: 1;
  transform: scale(1) translateY(0);
}

/* 全局重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 确保HTML和BODY完全消除空白 */
html,
body {
  margin: 0 !important;
  padding: 0 !important;
  height: 100%;
  width: 100%;
  border: 0;
  outline: 0;
}

/* 强制重置可能影响布局的元素 */
html {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

body {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

/* 确保页面第一个元素不会影响导航栏 */
body>*:first-child {
  margin-top: 0 !important;
  padding-top: 0;
}

/* 消除任何可能的默认浏览器样式 */
nav,
header {
  margin: 0 !important;
  padding: 0;
}

/* 兼容性重置 - 消除webkit和其他浏览器的默认样式 */
* {
  -webkit-margin-before: 0;
  -webkit-margin-after: 0;
  -webkit-margin-start: 0;
  -webkit-margin-end: 0;
  -webkit-padding-before: 0;
  -webkit-padding-after: 0;
  -webkit-padding-start: 0;
  -webkit-padding-end: 0;
}

/* 浏览器兼容性修复 */
::-webkit-scrollbar {
  width: 0;
}

/* 移动端视口修复 */
@-webkit-viewport {
  width: device-width;
}

@-moz-viewport {
  width: device-width;
}

@-ms-viewport {
  width: device-width;
}

@-o-viewport {
  width: device-width;
}

@viewport {
  width: device-width;
}

:root {
  /* 现代科技色彩主题 */
  --primary-blue: #0066ff;
  --secondary-blue: #003d99;
  --accent-cyan: #00d4ff;
  --dark-blue: #001f3f;
  --text-primary: #1a1a1a;
  --text-secondary: #666666;
  --text-light: #999999;
  --bg-white: #ffffff;
  --bg-light: #f8fafb;
  --bg-dark: #0a0f1a;
  --border-light: #e5e7eb;
  --border-dark: #374151;

  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, #0066ff 0%, #00d4ff 100%);
  --gradient-dark: linear-gradient(135deg, #0a0f1a 0%, #1e293b 100%);
  --gradient-light: linear-gradient(135deg, #f8fafb 0%, #ffffff 100%);

  /* 字体 */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  /* 间距 */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 2rem;
  --spacing-lg: 3rem;
  --spacing-xl: 5rem;
  --spacing-xxl: 8rem;

  /* 阴影 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.15);

  /* 圆角 */
  --radius-sm: 6px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
}

body {
  font-family: var(--font-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
  scroll-behavior: smooth;
  margin: 0 !important;
  padding: 0 !important;
  position: relative;
  top: 0;
  left: 0;
}

/* 全局容器设置 - 消除白边 */
section {
  padding-left: 0;
  padding-right: 0;
}

main {
  padding: 0;
  margin: 0;
}

.container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
}

/* 导航栏样式 */
.navbar {
  position: fixed;
  top: 0 !important;
  left: 0;
  width: 100%;
  background: #fff;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 9999;
  transition: all 0.3s ease;
  margin: 0 !important;
  padding: 0;
}

.navbar.scrolled {
  background: #fff;
  backdrop-filter: blur(30px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.nav-container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 85px;
}

.nav-logo {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-blue);
  text-decoration: none;
}

.logo-image {
  width: 110px;
  height: 110px;
  object-fit: contain;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.logo-image:hover {
  transform: scale(1.05);
}

.ai-icon {
  font-size: 1.5rem;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.nav-menu {
  display: flex;
  list-style: none;
  align-items: center;
  gap: 20px;
}

.nav-item {
  position: relative;
}

/* 导航链接统一样式 */
.nav-link {
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem !important;
  /* 强制统一字体大小 */
  transition: all 0.3s ease;
  padding: 0.6rem 1.2rem;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #1a1a1a;
  text-shadow: none;
}

.navbar.scrolled .nav-link {
  color: #1a1a1a;
  text-shadow: none;
}

.nav-link:hover,
.nav-link.active {
  color: #00d4ff;
  background: rgba(255, 255, 255, 0.1);
}

.navbar.scrolled .nav-link:hover,
.navbar.scrolled .nav-link.active {
  color: var(--primary-blue);
  background: rgba(0, 102, 255, 0.05);
}

/* 下拉菜单样式 */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: #ffffff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  padding: 8px 0;
  min-width: 180px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
  border: 1px solid #e5e7eb;
  display: none !important;
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  padding: 8px 16px;
  color: #6b7280;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.4;
}

.dropdown-item:hover {
  background: #f3f4f6;
  color: #0066ff;
}

/* 下拉箭头旋转 */
.dropdown:hover .nav-link .fa-chevron-down {
  transform: rotate(180deg);
}

.nav-link .fa-chevron-down {
  font-size: 0.8rem;
  transition: transform 0.3s ease;
  color: inherit;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.nav-toggle span {
  width: 25px;
  height: 2px;
  background: #ffffff;
  transition: all 0.3s ease;
}

/* 视频背景Hero区域 */
.hero-video-section {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  min-height: 600px;
  overflow: hidden;
  color: #ffffff;
  display: flex;
  align-items: flex-end;
  z-index: 1;
  transition: opacity 0.3s ease;
}

/* 视频区域占位 - 为首屏banner提供滚动空间 */
.hero-spacer {
  width: 100%;
  height: 100vh;
  min-height: 600px;
  margin: 0;
  padding: 0;
  background: transparent;
}

/* 视频背景 */
.hero-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -2;
}

/* 视频遮罩层 - 已移除 */
/* .video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: -1;
} */

/* 内容区域 */
.hero-content {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 初始内容 */
.hero-initial-content {
  position: absolute;
  bottom: 120px;
  left: 0;
  right: 0;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.hero-initial-content.fade-out {
  opacity: 0;
  transform: translateY(-50px);
}

.hero-text {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
  width: 100%;
  box-sizing: border-box;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 20px;
  color: #ffffff;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
}

.hero-subtitle {
  font-size: 1.3rem;
  font-weight: 400;
  line-height: 1.6;
  margin-bottom: 30px;
  color: #f8f9fa;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.7);
}

.hero-logo {
  margin-top: 20px;
}

.company-logo {
  height: 60px;
  width: auto;
  filter: drop-shadow(2px 2px 8px rgba(0, 0, 0, 0.5));
}

/* 滚动后内容 */
.hero-scroll-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  padding: 0 max(3%, 30px);
}

.hero-scroll-content.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.hero-scroll-content.slide-out-up {
  opacity: 0;
  transform: translateY(-50px);
}

.hero-scroll-content.slide-out-down {
  opacity: 0;
  transform: translateY(50px);
}

/* 左右分栏布局 */
.hero-split-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  width: 100%;
  max-width: 95%;
  margin: 0 auto;
  align-items: flex-start;
  padding: 0 max(3%, 30px);
}

/* 左侧内容 */
.hero-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0;
  margin-top: -80px;
}

.hero-left-text {
  text-align: left;
}

.hero-left-line {
  color: #ffffff;
  font-weight: 700;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
  margin-bottom: 10px;
}

.hero-left-line:nth-child(1) {
  font-size: 4rem;
  line-height: 1;
}

.hero-left-line:nth-child(2) {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1;
}

.hero-left-line:nth-child(3) {
  font-size: 4rem;
  line-height: 1;
}

/* 右侧内容 */
.hero-right {
  padding: 0;
  text-align: left;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-top: 60px;
}

.hero-right-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 30px;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
  text-align: left;
}

.hero-right-description {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
  text-align: left;
}

.hero-right-description p {
  font-size: 1.4rem;
  line-height: 1.6;
  color: #f8f9fa;
  margin-bottom: 20px;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.7);
}

.hero-right-description p:last-child {
  margin-bottom: 0;
}

/* 第三模块样式 */
.hero-third-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0 max(3%, 30px);
}

.hero-third-content.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.hero-third-content.slide-out-down {
  opacity: 0;
  transform: translateY(50px);
}

.hero-third-layout {
  width: 100%;
  max-width: 95%;
  margin: 0 auto;
  height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 120px max(3%, 30px) 120px max(3%, 30px);
}

.hero-third-header {
  align-self: flex-start;
}

.hero-third-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 15px;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
  letter-spacing: 2px;
}

.hero-third-subtitle {
  font-size: 1.8rem;
  color: #ffffff;
  font-weight: 400;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
  margin: 0;
}

.hero-third-stats {
  align-self: flex-start;
  display: flex;
  gap: 80px;
  margin-top: 40px;
}

.stat-group {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.stat-item-third {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.hero-third-content.fade-in .stat-item-third {
  opacity: 1;
  transform: translateY(0);
}

.hero-third-content.fade-in .stat-item-third:nth-child(1) {
  transition-delay: 0.2s;
}

.hero-third-content.fade-in .stat-item-third:nth-child(2) {
  transition-delay: 0.4s;
}

.hero-third-content.fade-in .stat-group:nth-child(2) .stat-item-third:nth-child(1) {
  transition-delay: 0.6s;
}

.hero-third-content.fade-in .stat-group:nth-child(2) .stat-item-third:nth-child(2) {
  transition-delay: 0.8s;
}

.stat-number-container {
  position: relative;
  display: inline-block;
}

.stat-number {
  font-size: 5.5rem;
  font-weight: 800;
  color: #00d4ff;
  line-height: 1;
  text-shadow: 0 0 30px rgba(0, 212, 255, 0.8), 0 0 60px rgba(0, 212, 255, 0.6);
  animation: numberPulse 2s ease-in-out infinite;
}

.stat-unit {
  position: absolute;
  top: 0;
  right: -1.3em;
  font-size: 1.8rem;
  color: #00d4ff;
  opacity: 0.9;
  transform: translateY(-0.3em);
  font-weight: 600;
  text-shadow: 0 0 15px rgba(0, 212, 255, 0.6);
}

.stat-label {
  font-size: 1.4rem;
  color: #ffffff;
  margin-top: 12px;
  opacity: 1;
  font-weight: 600;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
  letter-spacing: 0.5px;
}

@keyframes numberPulse {

  0%,
  100% {
    text-shadow: 0 0 30px rgba(0, 212, 255, 0.8), 0 0 60px rgba(0, 212, 255, 0.6);
    transform: scale(1);
  }

  50% {
    text-shadow: 0 0 40px rgba(0, 212, 255, 1), 0 0 80px rgba(0, 212, 255, 0.8);
    transform: scale(1.05);
  }
}

/* 大屏幕优化 */
@media (min-width: 1024px) {
  .stat-number {
    font-size: 6.5rem;
  }

  .stat-unit {
    font-size: 2.2rem;
  }

  .stat-label {
    font-size: 1.6rem;
  }
}

/* 滚动进度指示器 */
.scroll-progress-indicator {
  position: absolute;
  bottom: 40px;
  left: 30px;
  width: 200px;
}

.progress-bar {
  width: 100%;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  width: 0%;
  height: 100%;
  background: linear-gradient(90deg, #00d4ff, #0066ff);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-initial-content {
    bottom: 80px;
    left: 0;
    right: 0;
  }

  .hero-scroll-content {
    padding: 0 max(3%, 15px);
  }

  .hero-split-layout {
    grid-template-columns: 1fr;
    gap: 40px;
    max-width: 95%;
    padding: 0 max(3%, 15px);
  }

  .hero-left {
    justify-content: center;
    order: 2;
    margin-top: 0;
  }

  .hero-left-text {
    text-align: center;
  }

  .hero-right {
    padding-left: 0;
    order: 1;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 0;
  }

  .hero-left-line:nth-child(1),
  .hero-left-line:nth-child(2),
  .hero-left-line:nth-child(3) {
    font-size: 3rem;
  }

  .hero-right-title {
    font-size: 2rem;
  }

  .hero-right-description {
    max-width: 95%;
    margin: 0 auto;
    padding: 0 max(3%, 15px);
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .company-logo {
    height: 45px;
  }

  /* 第三阶段数字优化 */
  .hero-third-title {
    font-size: 2.8rem;
  }

  .hero-third-subtitle {
    font-size: 1.3rem;
  }

  .stat-number {
    font-size: 4.5rem;
  }

  .stat-unit {
    font-size: 1.6rem;
  }

  .stat-label {
    font-size: 1.3rem;
  }

  .scroll-progress-indicator {
    left: 15px;
    width: 150px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .company-logo {
    height: 40px;
  }

  .hero-initial-content {
    bottom: 60px;
    left: 0;
    right: 0;
  }

  .hero-scroll-content {
    padding: 0 max(3%, 10px);
  }

  .hero-split-layout {
    gap: 30px;
    max-width: 95%;
    padding: 0 max(3%, 30px);
  }

  .hero-left-line:nth-child(1),
  .hero-left-line:nth-child(2),
  .hero-left-line:nth-child(3) {
    font-size: 2.5rem;
  }

  .hero-right-title {
    font-size: 1.8rem;
  }

  .hero-right-description p {
    font-size: 1.1rem;
  }

  .hero-right {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 0;
  }

  .hero-third-content {
    padding: 0 max(3%, 10px);
  }

  .hero-third-layout {
    padding: 60px 0 80px 0;
    justify-content: flex-start;
    gap: 60px;
  }

  .hero-third-title {
    font-size: 2.2rem;
  }

  .hero-third-subtitle {
    font-size: 1.1rem;
  }

  .hero-third-stats {
    gap: 30px;
    flex-direction: column;
  }

  .stat-group {
    flex-direction: row;
    gap: 30px;
  }

  .stat-number {
    font-size: 3.5rem;
  }

  .stat-unit {
    font-size: 1.4rem;
    right: -1.1em;
  }

  .stat-label {
    font-size: 1.2rem;
  }

  .scroll-progress-indicator {
    left: 10px;
    width: 120px;
  }
}

/* 保留BTN样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: white;
  color: var(--primary-blue);
  border: 2px solid var(--border-light);
}

.btn-secondary:hover {
  background: var(--bg-light);
  transform: translateY(-2px);
}

.monitoring-illustration {
  position: relative;
  width: 400px;
  height: 400px;
}

.central-hub {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: 50%;
  box-shadow: 0 0 40px rgba(0, 102, 255, 0.4);
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

.sensor-network {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.sensor {
  position: absolute;
  width: 40px;
  height: 40px;
  background: rgba(0, 212, 255, 0.8);
  border-radius: 50%;
  animation: sensor-pulse 3s ease-in-out infinite;
}

.sensor-1 {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.sensor-2 {
  top: 20%;
  right: 20%;
  animation-delay: 0.5s;
}

.sensor-3 {
  bottom: 20%;
  left: 20%;
  animation-delay: 1s;
}

.sensor-4 {
  bottom: 20%;
  right: 20%;
  animation-delay: 1.5s;
}



/* 滚动指示器 */
.scroll-indicator {
  position: absolute;
  bottom: 40px;
  right: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-light);
  font-size: 0.9rem;
  z-index: 10;
  cursor: pointer;
}

.scroll-arrow {
  animation: bounce 2s infinite;
}

/* 公司介绍区域 */
.about-section {
  padding: 0;
  background: linear-gradient(135deg, #f8fafb 0%, #ffffff 50%, #f0f4f8 100%);
  position: relative;
  overflow: hidden;
  min-height: 90vh;
  margin: 0;
}

.about-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 20%, rgba(0, 102, 255, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 212, 255, 0.03) 0%, transparent 50%);
  z-index: 0;
  pointer-events: none;
}

.about-content {
  position: relative;
  z-index: 1;
  min-height: 70vh;
  display: flex;
  align-items: flex-start;
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
}

/* 左侧内容区域 */
.company-info {
  flex: 1;
  max-width: 50%;
  padding-right: var(--spacing-lg);
}

/* 公司信息头部 */
.company-header {
  margin-bottom: var(--spacing-lg);
}

/* 公司名称 */
.company-name {
  font-size: clamp(2.2rem, 4vw, 3.2rem);
  font-weight: 700;
  line-height: 1.3;
  margin-bottom: var(--spacing-sm);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.company-slogan {
  font-size: 1.1rem;
  color: var(--text-secondary);
  font-weight: 500;
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

/* 公司描述 */
.company-description {
  margin-bottom: var(--spacing-xl);
}

.company-description p {
  color: var(--text-secondary);
  line-height: 1.8;
  font-size: 1.05rem;
  margin-bottom: var(--spacing-md);
}

.company-description p:last-child {
  margin-bottom: 0;
}

/* 统计数据 */
.company-stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: var(--spacing-md);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  min-height: 80px;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  background: rgba(0, 102, 255, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-blue);
  font-size: 1.2rem;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 0.25rem;
  line-height: 1;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
  line-height: 1.2;
}

/* 右侧建筑图片 */
.building-visual {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 50%;
  max-width: 800px;
  overflow: hidden;
}

.building-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
}

.building-image:hover {
  transform: scale(1.05);
}

.highlight {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
}

/* About section响应式设计 */
@media (max-width: 1200px) {
  .about-section {
    min-height: auto;
    padding: var(--spacing-xl) 0;
  }

  .building-visual {
    width: 55%;
    max-width: 700px;
  }

  .company-stats {
    gap: var(--spacing-md);
  }

  .stat-item {
    min-height: 70px;
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .stat-icon {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }

  .stat-number {
    font-size: 1.3rem;
  }
}

@media (max-width: 992px) {
  .about-content {
    padding-top: var(--spacing-xl);
  }

  .company-info {
    max-width: 55%;
    margin-right: auto;
  }

  .building-visual {
    width: 55%;
    max-width: 500px;
  }

  .company-stats {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}

@media (max-width: 768px) {
  .about-section {
    padding: var(--spacing-lg) 0;
  }

  .about-content {
    flex-direction: column;
  }

  .company-info {
    max-width: 100%;
    order: 2;
    padding-right: 0;
  }

  .building-visual {
    position: relative;
    order: 1;
    width: 100%;
    max-width: 400px;
    margin: 0 auto var(--spacing-lg) auto;
  }

  .company-name {
    font-size: clamp(1.8rem, 6vw, 2.5rem);
    text-align: center;
  }

  .company-slogan {
    text-align: center;
  }

  .company-description {
    text-align: left;
  }

  .company-description p {
    font-size: 1rem;
    line-height: 1.6;
  }
}

@media (max-width: 480px) {
  .building-visual {
    max-width: 320px;
  }

  .company-name {
    font-size: clamp(1.6rem, 8vw, 2rem);
  }

  .company-slogan {
    font-size: 1rem;
  }

  .company-description p {
    font-size: 0.9rem;
  }

  .company-stats {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .stat-item {
    flex-direction: column;
    text-align: center;
    padding: var(--spacing-md);
    min-height: auto;
  }

  .stat-icon {
    margin-bottom: var(--spacing-xs);
  }

  .stat-number {
    font-size: 1.3rem;
  }

  .stat-label {
    font-size: 0.85rem;
  }
}

/* 高质效的数字监测解决方案区域 */
.solutions-section {
  min-height: 100vh;
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 0 !important;
  margin: 0;
  width: 100vw;
  box-sizing: border-box;
}

.solutions-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.02) 0%, transparent 30%);
  pointer-events: none;
}

.solutions-container {
  width: 100%;
  max-width: none;
  text-align: center;
  position: relative;
  z-index: 10;
  padding: 0 2vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 40px;
  box-sizing: border-box;
}

/* 动态毛玻璃大圆球背景 */
.dynamic-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.glass-orb {
  position: absolute;
  border-radius: 50%;
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  animation: floatOrb 20s ease-in-out infinite;
}

.orb-1 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 50%, transparent 100%);
  top: -200px;
  left: -200px;
  animation-delay: 0s;
}

.orb-2 {
  width: 350px;
  height: 350px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.08) 0%, rgba(139, 92, 246, 0.04) 50%, transparent 100%);
  top: -100px;
  right: -150px;
  animation-delay: 5s;
}

.orb-3 {
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.06) 0%, rgba(16, 185, 129, 0.03) 50%, transparent 100%);
  bottom: -250px;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 10s;
}

.orb-4 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.09) 0%, rgba(99, 102, 241, 0.04) 50%, transparent 100%);
  top: 50%;
  right: -100px;
  transform: translateY(-50%);
  animation-delay: 15s;
}

/* 标题区域 */
.solutions-header {
  flex: 1.2;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  gap: 30px;
  min-height: 0;
  padding-bottom: 0px;
}

/* 主标题样式 */
.solutions-main-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.1;
  letter-spacing: -0.02em;
  margin: 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* 添加动画过渡效果 */
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.8s ease;
}

/* 副标题样式 */
.solutions-subtitle {
  font-size: 1.3rem;
  color: #475569;
  line-height: 1.4;
  margin: 0 auto;
  max-width: 90vw;
  font-weight: 400;
  white-space: normal;
  text-align: center;
}

/* 架构图片容器 */
.architecture-image-container {
  position: relative;
  margin-top: 0;
  flex: 2.8;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  min-height: 0;
}

.architecture-img {
  width: 80%;
  max-width: none;
  max-height: 65vh;
  height: auto;
  object-fit: contain;
  border-radius: 0;
  box-shadow: none;
  transition: all 0.3s ease;
  background: none;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  border: none;
  display: block;
  position: relative;
  z-index: 2;
  margin-bottom: 0;
}

/* 星尘AI+病媒监测平台发光效果 */
.architecture-image-container::before {
  content: '';
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 45vw;
  height: 28vh;
  background: radial-gradient(ellipse,
      rgba(59, 130, 246, 0.4) 0%,
      rgba(59, 130, 246, 0.25) 30%,
      rgba(139, 92, 246, 0.15) 60%,
      transparent 80%);
  border-radius: 50%;
  z-index: 1;
  animation: coreGlow 3s ease-in-out infinite alternate;
  pointer-events: none;
}

.architecture-image-container::after {
  content: '';
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 56vw;
  height: 36vh;
  background: radial-gradient(ellipse,
      rgba(16, 185, 129, 0.2) 0%,
      rgba(34, 197, 94, 0.1) 40%,
      transparent 70%);
  border-radius: 50%;
  z-index: 0;
  animation: coreGlow 4s ease-in-out infinite alternate-reverse;
  pointer-events: none;
}

@keyframes coreGlow {
  0% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }

  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* 毛玻璃球动画 */
@keyframes floatOrb {

  0%,
  100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.6;
  }

  25% {
    transform: translate(30px, -20px) scale(1.1);
    opacity: 0.8;
  }

  50% {
    transform: translate(-20px, 30px) scale(0.9);
    opacity: 0.7;
  }

  75% {
    transform: translate(40px, 10px) scale(1.05);
    opacity: 0.9;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .solutions-container {
    padding: 0 4vw;
    gap: 35px;
  }

  .solutions-header {
    gap: 25px;
    padding-bottom: 20px;
    padding-top: 20px;
  }

  .solutions-main-title {
    font-size: 2.2rem;
    margin: 0 auto;
    padding: 0 15px;
  }

  .solutions-subtitle {
    font-size: 1rem;
    white-space: normal;
    padding: 0 10px;
  }

  .architecture-img {
    width: 85%;
    max-height: 55vh;
  }

  .architecture-image-container::before {
    width: 43vw;
    height: 26vh;
    top: 35%;
  }

  .architecture-image-container::after {
    width: 53vw;
    height: 34vh;
    top: 35%;
  }

  .orb-1,
  .orb-2,
  .orb-3,
  .orb-4 {
    transform: scale(0.7);
  }
}

@media (max-width: 480px) {
  .solutions-container {
    padding: 0 5vw;
    gap: 30px;
  }

  .solutions-header {
    gap: 20px;
    padding-bottom: 25px;
    padding-top: 25px;
  }

  .solutions-main-title {
    font-size: 1.7rem;
    margin: 0 auto;
    padding: 0 20px;
  }

  .solutions-subtitle {
    font-size: 0.9rem;
    white-space: normal;
    padding: 0 15px;
  }

  .architecture-img {
    width: 80%;
    max-height: 50vh;
  }

  .architecture-image-container::before {
    width: 40vw;
    height: 24vh;
    top: 35%;
  }

  .architecture-image-container::after {
    width: 50vw;
    height: 30vh;
    top: 35%;
  }

  .orb-1,
  .orb-2,
  .orb-3,
  .orb-4 {
    transform: scale(0.5);
  }
}

/* 中等屏幕优化 */
@media (min-width: 1024px) and (max-width: 1399px) {
  .solutions-container {
    padding: 0 2vw;
  }

  .solutions-header {
    padding-top: 15px;
    padding-left: 15px;
    padding-right: 15px;
  }

  .architecture-img {
    width: 85%;
    max-height: 65vh;
  }

  .architecture-image-container::before {
    width: 44vw;
    height: 27vh;
  }

  .architecture-image-container::after {
    width: 55vw;
    height: 35vh;
  }

  .solutions-main-title {
    font-size: 3.2rem;
    padding: 0 10px;
  }

  .solutions-subtitle {
    font-size: 1.3rem;
    max-width: 85vw;
    padding: 0 10px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
  .solutions-container {
    padding: 0 1.5vw;
  }

  .solutions-header {
    padding-top: 10px;
    padding-left: 10px;
    padding-right: 10px;
  }

  .architecture-img {
    width: 85%;
    max-height: 70vh;
  }

  .architecture-image-container::before {
    width: 45vw;
    height: 28vh;
  }

  .architecture-image-container::after {
    width: 56vw;
    height: 36vh;
  }

  .solutions-main-title {
    font-size: 3.8rem;
    padding: 0 5px;
  }

  .solutions-subtitle {
    font-size: 1.4rem;
    max-width: 80vw;
    padding: 0 5px;
  }
}

/* 科技装饰叠加层 - 隐藏 */
.tech-overlay {
  display: none;
}

/* 扫描线效果 */
.scanning-line {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(0, 212, 255, 0.8) 50%,
      transparent 100%);
  animation: scan-across 4s ease-in-out infinite;
  z-index: 5;
}

@keyframes scan-across {
  0% {
    left: -100%;
  }

  50% {
    left: 100%;
  }

  100% {
    left: -100%;
  }
}

/* 数据点效果 */
.data-dots {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20% 30%, rgba(0, 212, 255, 0.8), transparent),
    radial-gradient(2px 2px at 40% 70%, rgba(0, 102, 255, 0.6), transparent),
    radial-gradient(1px 1px at 90% 40%, rgba(138, 43, 226, 0.7), transparent),
    radial-gradient(1px 1px at 60% 90%, rgba(0, 255, 127, 0.5), transparent);
  background-size: 200px 200px, 150px 150px, 100px 100px, 180px 180px;
  animation: data-flow 8s linear infinite;
  opacity: 0.6;
}

@keyframes data-flow {
  0% {
    background-position: 0px 0px, 0px 0px, 0px 0px, 0px 0px;
  }

  100% {
    background-position: 200px 200px, -150px 150px, 100px -100px, -180px 180px;
  }
}

/* 脉冲环效果 */
.pulse-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
}

.pulse-rings::before,
.pulse-rings::after {
  content: '';
  position: absolute;
  border: 2px solid rgba(0, 212, 255, 0.3);
  border-radius: 50%;
  animation: pulse-ring 3s ease-in-out infinite;
}

.pulse-rings::before {
  width: 100px;
  height: 100px;
  top: 50px;
  left: 50px;
  animation-delay: 0s;
}

.pulse-rings::after {
  width: 150px;
  height: 150px;
  top: 25px;
  left: 25px;
  animation-delay: 1.5s;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }

  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 动态科技背景 - 简洁版 */
.tech-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

/* 电路网格 - 简洁 */
.circuit-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 102, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.05) 1px, transparent 1px);
  background-size: 80px 80px;
  animation: grid-move 30s linear infinite;
  opacity: 0.2;
}

@keyframes grid-move {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(80px, 80px);
  }
}

/* 移除复杂的浮动粒子、能量波纹、神经网络 */
.floating-particles,
.energy-waves,
.neural-network {
  display: none;
}

/* 浮动球形图片 */
.floating-orbs {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.tech-orb {
  position: absolute;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.2));
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}

.orb-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.orb-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 140px;
  height: 140px;
  background: radial-gradient(circle,
      rgba(0, 212, 255, 0.3) 0%,
      rgba(0, 102, 255, 0.2) 40%,
      transparent 70%);
  border-radius: 50%;
  animation: orb-pulse 2s ease-in-out infinite alternate;
  z-index: -1;
}

@keyframes orb-pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }

  100% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 1;
  }
}

.orb-1 {
  top: 20%;
  left: 15%;
  animation: float-orb-1 8s ease-in-out infinite;
}

.orb-2 {
  top: 60%;
  right: 20%;
  animation: float-orb-2 6s ease-in-out infinite;
  animation-delay: 1s;
}

.orb-3 {
  bottom: 30%;
  left: 25%;
  animation: float-orb-3 7s ease-in-out infinite;
  animation-delay: 2s;
}

.orb-4 {
  top: 15%;
  right: 35%;
  animation: float-orb-4 9s ease-in-out infinite;
  animation-delay: 3s;
}

.orb-5 {
  bottom: 15%;
  right: 45%;
  animation: float-orb-5 5s ease-in-out infinite;
  animation-delay: 1.5s;
}

@keyframes float-orb-1 {

  0%,
  100% {
    transform: translateY(0px) translateX(0px);
  }

  25% {
    transform: translateY(-20px) translateX(10px);
  }

  50% {
    transform: translateY(-10px) translateX(-15px);
  }

  75% {
    transform: translateY(-25px) translateX(5px);
  }
}

@keyframes float-orb-2 {

  0%,
  100% {
    transform: translateY(0px) translateX(0px);
  }

  33% {
    transform: translateY(-15px) translateX(-10px);
  }

  66% {
    transform: translateY(-30px) translateX(20px);
  }
}

@keyframes float-orb-3 {

  0%,
  100% {
    transform: translateY(0px) translateX(0px);
  }

  50% {
    transform: translateY(-25px) translateX(-20px);
  }
}

@keyframes float-orb-4 {

  0%,
  100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }

  25% {
    transform: translateY(-10px) translateX(15px) rotate(90deg);
  }

  50% {
    transform: translateY(-30px) translateX(-5px) rotate(180deg);
  }

  75% {
    transform: translateY(-15px) translateX(-20px) rotate(270deg);
  }
}

@keyframes float-orb-5 {

  0%,
  100% {
    transform: translateY(0px) translateX(0px) scale(1);
  }

  20% {
    transform: translateY(-20px) translateX(10px) scale(1.1);
  }

  40% {
    transform: translateY(-5px) translateX(-15px) scale(0.9);
  }

  60% {
    transform: translateY(-25px) translateX(20px) scale(1.05);
  }

  80% {
    transform: translateY(-10px) translateX(-10px) scale(0.95);
  }
}

/* 道路系统 */
.road-system {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: translateZ(1px);
}

.road {
  position: absolute;
  background: #666;
  transform: rotateX(-90deg);
}

.main-road {
  top: 50%;
  left: 0;
  width: 100%;
  height: 12px;
  transform: translateY(-50%) rotateX(-90deg) translateZ(1px);
}

.side-road-1 {
  top: 0;
  left: 30%;
  width: 8px;
  height: 100%;
  transform: translateX(-50%) rotateX(-90deg) translateZ(1px);
}

.side-road-2 {
  top: 0;
  right: 30%;
  width: 8px;
  height: 100%;
  transform: translateX(50%) rotateX(-90deg) translateZ(1px);
}

/* 河流 */
.river {
  position: absolute;
  bottom: 0;
  left: 20%;
  width: 60%;
  height: 20px;
  transform: rotateX(-90deg) translateZ(1px);
  overflow: hidden;
}

.water-flow {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      #4285f4 0%,
      #5294f5 25%,
      #6ba3f6 50%,
      #5294f5 75%,
      #4285f4 100%);
  animation: water-flow 4s ease-in-out infinite;
}

/* 场景标记 */
.scenario-marker {
  position: absolute;
  top: -80px;
  left: 50%;
  transform: translateX(-50%) translateZ(60px);
  z-index: 10;
  cursor: pointer;
}

.marker-icon {
  width: 40px;
  height: 40px;
  background: rgba(0, 102, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  box-shadow: 0 4px 12px rgba(0, 102, 255, 0.4);
  animation: marker-pulse 2s ease-in-out infinite;
  transition: all 0.3s ease;
}

.marker-icon:hover {
  transform: scale(1.2);
  box-shadow: 0 6px 20px rgba(0, 102, 255, 0.6);
}

.marker-label {
  position: absolute;
  top: 45px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.marker-tooltip {
  position: absolute;
  top: -120px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 100;
}

.marker-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 8px solid transparent;
  border-top-color: white;
}

.scenario-marker:hover .marker-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-10px);
}

.marker-tooltip h4 {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.marker-tooltip p {
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.4;
  margin: 0;
}

/* 场景控制器 */
.scene-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  z-index: 10;
}

.scene-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--border-light);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.scene-control.active,
.scene-control:hover {
  background: var(--primary-blue);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 102, 255, 0.3);
}

/* 第三屏：发展历程 */
.timeline-section {
  min-height: 100vh;
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 0 !important;
  margin: 0;
  width: 100vw;
  box-sizing: border-box;
}

.timeline-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.02) 0%, transparent 30%);
  pointer-events: none;
}

.timeline-container {
  width: 100%;
  max-width: none;
  text-align: center;
  position: relative;
  z-index: 10;
  padding: 0 max(3%, 30px);
  /* 与导航栏保持一致的宽度限制 */
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 0;
  box-sizing: border-box;
}

/* 标题区域 */
.timeline-header {
  flex: 0 0 25vh;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  margin-bottom: 0;
  padding-bottom: 20px;
  padding-top: 20px;
  padding-left: 20px;
  padding-right: 20px;
  transform: translateY(-40px);
}

/* 横向时间线内容 */
.timeline-content {
  flex: 0 0 50vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0;
}

.timeline-track {
  position: relative;
  width: 100%;
  max-width: none;
  /* 移除最大宽度限制，像合作logo一样 */
  margin: 0 auto;
  padding: 80px 20px;
  /* 减少左右内边距，让内容在大屏幕上更宽 */
  min-height: 550px;
  /* 增加最小高度以适应更大的上下间距 */
  z-index: 1;
  /* 确保时间轨道在正确的层级 */
}

/* 水平时间线 */
.timeline-track::before {
  content: '';
  position: absolute;
  left: 40px;
  /* 减少左边距，在大屏幕上延伸更远 */
  right: 40px;
  /* 减少右边距，在大屏幕上延伸更远 */
  top: 50%;
  height: 4px;
  background: linear-gradient(90deg,
      var(--primary-blue) 0%,
      var(--accent-cyan) 25%,
      var(--primary-blue) 50%,
      var(--accent-cyan) 75%,
      var(--primary-blue) 100%);
  transform: translateY(-50%);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  border-radius: 2px;
  z-index: 0;
  /* 确保线条在最底层 */
}

/* 轮播容器 */
.timeline-track {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 550px;
  /* 增加高度以适应更大的上下间距 */
  max-width: none;
  /* 确保在大屏幕上充分展开 */
}

/* 轮播轨道 */
.timeline-items-container {
  display: flex;
  align-items: center;
  height: 100%;
  /* 宽度由JavaScript动态设置 */
  z-index: 2;
  padding: 10px 0;
  /* 确保容器在时间线之上 */
}

/* 时间节点 - 水平排列，和合作logo等宽 */
.timeline-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 3;
  /* 确保时间线项目在正确层级 */
  margin: 0 40px;
  transition: all 0.3s ease;
  height: 100%;
}




/* 奇数项卡片在时间线上方 */
.timeline-item:nth-child(odd) {
  justify-content: flex-start;
  flex-direction: column;
}

.timeline-item:nth-child(odd) .timeline-card {
  position: relative;
  margin-bottom: 100px;
  /* 增加到100px，让上下交错更明显 */
  order: 1;
}

.timeline-item:nth-child(odd) .timeline-marker {
  order: 2;
  position: relative;
  margin-top: -40px;
  /* 增加向上移动的距离，更靠近上方卡片 */
  margin-bottom: 0;
}

/* 偶数项卡片在时间线下方 */
.timeline-item:nth-child(even) {
  justify-content: flex-end;
  flex-direction: column;
}

.timeline-item:nth-child(even) .timeline-card {
  position: relative;
  margin-top: 100px;
  /* 增加到100px，让上下交错更明显 */
  order: 2;
}

.timeline-item:nth-child(even) .timeline-marker {
  order: 1;
  position: relative;
  margin-bottom: -40px;
  /* 增加向下移动的距离，更靠近下方卡片 */
  margin-top: 0;
}

/* 时间标记 - 位于水平线上 */
.timeline-marker {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-blue), var(--accent-cyan));
  border: 3px solid rgba(255, 255, 255, 0.9);
  box-shadow:
    0 4px 16px rgba(0, 102, 255, 0.3),
    0 0 0 4px rgba(0, 212, 255, 0.1);
  transition: all 0.3s ease;
  z-index: 10;
  /* 确保标记点在最顶层 */
  flex-shrink: 0;
}

.timeline-marker::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background: #3b82f6;
  border-radius: 50%;
  opacity: 0.8;
}

.timeline-card {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  max-width: 280px;
  min-width: 240px;
  width: 100%;
  text-align: center;
  z-index: 5;
  /* 确保卡片在时间线上方 */
}

.timeline-card:hover {
  transform: translateY(-8px);
  box-shadow:
    0 20px 50px rgba(59, 130, 246, 0.12),
    0 0 30px rgba(59, 130, 246, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.95);
}

.timeline-card:hover .timeline-marker {
  transform: translateX(-50%) scale(1.2);
  background: linear-gradient(135deg, var(--accent-cyan), var(--primary-blue));
  box-shadow:
    0 6px 20px rgba(0, 212, 255, 0.4),
    0 0 0 6px rgba(0, 212, 255, 0.15);
}

/* 卡片箭头指向水平时间线 */
/* 奇数项连接箭头（向下指向时间线） */
.timeline-item:nth-child(odd) .timeline-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 15px 15px 0 15px;
  border-color: rgba(255, 255, 255, 0.15) transparent transparent transparent;
  transition: all 0.3s ease;
}

/* 偶数项连接箭头（向上指向时间线） */
.timeline-item:nth-child(even) .timeline-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 15px 15px 15px;
  border-color: transparent transparent rgba(255, 255, 255, 0.15) transparent;
  transition: all 0.3s ease;
}

.timeline-date {
  color: #3b82f6;
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 10px;
  letter-spacing: 0.5px;
}

.timeline-title {
  color: #1e293b;
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1.3;
}

.timeline-description {
  color: #64748b;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}


@media (max-width: 768px) {
  .timeline-items-container {
    overflow-y: auto;
    height: 100%;
    padding: 20px !important;
    flex-direction: column;
  }

  .timeline-track {
    padding: 0px;
    min-height: auto;
  }


  .timeline-container {
    padding: 0;
    height: auto;
    min-height: 100vh;
  }

  .timeline-item {
    margin: 20px 0 0;
    height: auto;
    max-width: none;
    width: 100%;
    justify-content: flex-start;
  }

  .timeline-marker {
    order: 1 !important;
    margin: 0 0 20px 0 !important;
  }

  .timeline-card {
    order: 2 !important;
    margin: 0 !important;
    max-width: 100%;
    width: 100%;
    box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .timeline-card::after {
    display: none;
  }

  .timeline-header {
    flex: 0 0 auto;
    padding: 20px;
    transform: translateY(-20px);
  }

  .timeline-content {
    flex: 1;
    padding: 24px;
  }

  .timeline-header {
    flex: 0 0 22vh;
  }


  .timeline-header .solutions-main-title {
    font-size: 2.2rem;
    padding: 0 5px;
  }


  .timeline-track::before {
    left: 50%;
    right: auto;
    top: 0;
    bottom: 0;
    width: 4px;
    height: auto;
    transform: translateX(-50%);
    background: linear-gradient(180deg,
        var(--primary-blue) 0%,
        var(--accent-cyan) 50%,
        var(--primary-blue) 100%);
  }



  .timeline-title {
    font-size: 1rem;
  }

  .timeline-description {
    font-size: 0.85rem;
  }


}


/* ================================
   合作机构轮播样式
   ================================ */

/* 合作机构区域 - 橙色框区域 */
.partners-section {
  width: 100%;
  flex: 0 0 30vh;
  margin-top: 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.partners-carousel {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 180px;
  margin-left: 0;
  background: transparent;
}

.partners-track {
  display: flex;
  align-items: center;
  height: 100%;
  /* 宽度由JavaScript动态设置 */
}

.partner-item {
  flex: 0 0 320px;
  height: 140px;
  margin: 0 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  padding: 20px;
  transition: all 0.3s ease;
}

.partner-item:hover {
  transform: translateY(-3px);
}

.partner-item img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  filter: grayscale(0.1);
  transition: all 0.3s ease;
}

.partner-item:hover img {
  filter: grayscale(0);
  transform: scale(1.08);
}

/* 轮播暂停效果 */
.partners-carousel:hover .partners-track {
  animation-play-state: paused;
}

/* 合作机构轮播响应式设计 */
@media (max-width: 1200px) {
  .partners-section {
    flex: 0 0 25vh;
  }

  .partners-carousel {
    height: 150px;
  }

  .partner-item {
    flex: 0 0 280px;
    height: 110px;
    margin: 0 35px;
  }

  .partners-track {
    /* 宽度由JavaScript动态设置 */
  }
}

@media (max-width: 768px) {
  .partners-section {
    margin-top: 0;
    flex: 0 0 20vh;
  }

  .partners-carousel {
    height: 120px;
  }

  .partner-item {
    flex: 0 0 200px;
    height: 80px;
    margin: 0 25px;
    padding: 10px;
  }


}

/* ================================
   产品中心页面样式
   ================================ */

/* 产品页面主容器 */
.products-main {
  padding-top: 80px;
  /* 导航栏高度 */
}

.products-main .header-right {
  max-width: calc(100% - 30px);
  margin: 0;
}

/* 产品中心头部 */
.products-hero {
  padding: 80px 0 60px;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

/* 浮动的毛玻璃球 - 限制在products区域 */
.products-hero::after {
  content: '';
  position: absolute;
  top: 15%;
  left: 8%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.12) 0%, rgba(139, 92, 246, 0.08) 50%, transparent 70%);
  backdrop-filter: blur(20px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: floatGlassBall1 15s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.products-hero .glass-ball-2 {
  position: absolute;
  top: 55%;
  right: 12%;
  width: 220px;
  height: 220px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.11) 0%, rgba(59, 130, 246, 0.07) 50%, transparent 70%);
  backdrop-filter: blur(18px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.25);
  animation: floatGlassBall2 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.products-hero .glass-ball-3 {
  position: absolute;
  top: 25%;
  right: 8%;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.09) 0%, rgba(99, 102, 241, 0.06) 50%, transparent 70%);
  backdrop-filter: blur(15px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  animation: floatGlassBall3 18s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.products-hero .glass-ball-4 {
  position: absolute;
  top: 70%;
  left: 45%;
  width: 190px;
  height: 190px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.08) 0%, rgba(99, 102, 241, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(12px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.18);
  animation: floatGlassBall4 22s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.products-hero .glass-ball-5 {
  position: absolute;
  top: 80%;
  left: 5%;
  width: 170px;
  height: 170px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.06) 50%, transparent 70%);
  backdrop-filter: blur(14px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.22);
  animation: floatGlassBall5 25s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.products-hero .glass-ball-6 {
  position: absolute;
  top: 20%;
  left: 65%;
  width: 130px;
  height: 130px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.08) 0%, rgba(99, 102, 241, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.15);
  animation: floatGlassBall6 19s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes floatGlassBall1 {

  0%,
  100% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.8;
  }

  25% {
    transform: translateY(-30px) translateX(25px) scale(1.1);
    opacity: 0.6;
  }

  50% {
    transform: translateY(40px) translateX(-15px) scale(0.9);
    opacity: 0.9;
  }

  75% {
    transform: translateY(-20px) translateX(30px) scale(1.05);
    opacity: 0.7;
  }
}

@keyframes floatGlassBall2 {

  0%,
  100% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.7;
  }

  30% {
    transform: translateY(35px) translateX(-30px) scale(1.2);
    opacity: 0.5;
  }

  60% {
    transform: translateY(-45px) translateX(20px) scale(0.8);
    opacity: 0.8;
  }

  90% {
    transform: translateY(25px) translateX(-35px) scale(1.1);
    opacity: 0.6;
  }
}

@keyframes floatGlassBall3 {

  0%,
  100% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.6;
  }

  20% {
    transform: translateY(-50px) translateX(35px) scale(1.3);
    opacity: 0.4;
  }

  40% {
    transform: translateY(30px) translateX(-40px) scale(0.7);
    opacity: 0.8;
  }

  80% {
    transform: translateY(-25px) translateX(30px) scale(1.1);
    opacity: 0.5;
  }
}

@keyframes floatGlassBall4 {

  0%,
  100% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.5;
  }

  35% {
    transform: translateY(-35px) translateX(40px) scale(0.9);
    opacity: 0.7;
  }

  65% {
    transform: translateY(25px) translateX(-30px) scale(1.2);
    opacity: 0.4;
  }

  85% {
    transform: translateY(-30px) translateX(45px) scale(1.05);
    opacity: 0.6;
  }
}

@keyframes floatGlassBall5 {

  0%,
  100% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.6;
  }

  20% {
    transform: translateY(-60px) translateX(50px) scale(1.1);
    opacity: 0.8;
  }

  40% {
    transform: translateY(50px) translateX(-35px) scale(0.8);
    opacity: 0.4;
  }

  60% {
    transform: translateY(-35px) translateX(40px) scale(1.3);
    opacity: 0.7;
  }

  80% {
    transform: translateY(65px) translateX(-25px) scale(0.9);
    opacity: 0.5;
  }
}

@keyframes floatGlassBall6 {

  0%,
  100% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.7;
  }

  25% {
    transform: translateY(40px) translateX(-35px) scale(1.2);
    opacity: 0.5;
  }

  50% {
    transform: translateY(-35px) translateX(30px) scale(0.7);
    opacity: 0.9;
  }

  75% {
    transform: translateY(30px) translateX(-40px) scale(1.1);
    opacity: 0.6;
  }
}

.products-header {
  position: relative;
  z-index: 2;
}

.header-content {
  display: flex;
  align-items: flex-start;
  gap: 80px;
  width: 100%;
}

.header-left {
  flex: 0 0 auto;
  min-width: 350px;
}

.header-right {
  flex: 1;
  max-width: calc(100% - 430px);
  margin-left: 120px;
}

.products-title {
  font-size: 8rem;
  font-weight: 800;
  color: #2d3748;
  margin: 0;
  line-height: 0.9;
  letter-spacing: -0.03em;
  font-family: 'Inter', sans-serif;
}

.products-subtitle {
  font-size: 1.5rem;
  color: #64748b;
  margin: 15px 0 0 0;
  font-weight: 400;
}

.solutions-info {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  backdrop-filter: none;
  box-shadow: none;
  position: relative;
  overflow: visible;
}

.solutions-title {
  font-size: 2.4rem;
  font-weight: 700;
  color: #6366f1;
  margin: 0 0 20px 0;
}

.solutions-description {
  font-size: 1.3rem;
  line-height: 1.8;
  color: #64748b;
  margin: 0;
  position: relative;
  z-index: 2;
}

.highlight-text {
  font-weight: 800;
  color: #1e293b;
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 产品展示区域 */
.products-showcase {
  padding: 80px 0;
  background: white;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

/* 产品卡片 */
.product-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  overflow: hidden;
  backdrop-filter: blur(20px);
  box-shadow:
    0 20px 50px rgba(99, 102, 241, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  position: relative;
}

.product-card:hover {
  box-shadow:
    0 30px 60px rgba(99, 102, 241, 0.15),
    0 0 40px rgba(99, 102, 241, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.95);
  border-color: rgba(99, 102, 241, 0.2);
}

/* 产品图片区域 */
.product-image {
  position: relative;
  height: 280px;
  /* 默认高度 */
  overflow: hidden;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

/* 响应式调整 - 从大屏幕到小屏幕 */
@media (min-width: 2000px) {
  .product-image {
    height: 420px;
    /* 超大屏幕 */
  }
}

@media (min-width: 1600px) and (max-width: 1999px) {
  .product-image {
    height: 380px;
    /* 大屏幕 */
  }
}

@media (min-width: 1400px) and (max-width: 1599px) {
  .product-image {
    height: 350px;
    /* 中等大屏幕 */
  }
}

@media (min-width: 1200px) and (max-width: 1399px) {
  .product-image {
    height: 320px;
    /* 标准大屏幕 */
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .product-image {
    height: 300px;
    /* 中等屏幕 */
  }
}

@media (max-width: 768px) {
  .product-image {
    height: 240px;
    /* 平板屏幕 */
  }
}

@media (max-width: 480px) {
  .product-image {
    height: 200px;
    /* 手机屏幕 */
  }
}

.product-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-img {
  transform: scale(1.05);
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.product-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #6366f1;
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2);
}

/* 产品内容 */
.product-content {
  padding: 30px 30px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 120px;
}

.product-info {
  flex: 1;
}

.product-category {
  display: inline-block;
  font-size: 0.85rem;
  font-weight: 600;
  color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
  padding: 4px 12px;
  border-radius: 15px;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.product-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.product-summary {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0;
  line-height: 1.4;
  font-weight: 400;
}

/* 产品展开按钮 */
.product-expand {
  flex: 0 0 auto;
  display: none;
}

.expand-btn {
  background: none;
  border: none;
  color: #6366f1;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.expand-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  color: #5856eb;
}

.expand-btn i {
  transition: transform 0.3s ease;
}

/* 产品详情 */
.product-details {
  padding: 15px 30px 30px;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
  margin-top: 0;
  display: block !important;
}

.product-description {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #64748b;
  margin: 0;
}

/* 产品特性标签 */
.product-features {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 25px;
}

.feature-tag {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

/* 产品按钮 */
.product-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.product-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
  background: linear-gradient(135deg, #5856eb, #7c3aed);
}

.product-btn i {
  transition: transform 0.3s ease;
}

.product-btn:hover i {
  transform: translateX(3px);
}

/* 技术与服务优势区域 */
.advantages-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
}

.advantages-section .container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
}

.advantages-section .section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 60px;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease;
}

.advantages-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
}

.subsection-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #6366f1;
  margin-bottom: 40px;
  text-align: center;
}

/* 技术优势区域 */
.tech-advantages-area {
  padding: 40px 30px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  opacity: 0;
  transform: translateX(-80px);
  transition: all 0.8s ease;
  backdrop-filter: blur(20px);
  box-shadow:
    0 20px 50px rgba(99, 102, 241, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.tech-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 25px;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 15px;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(40px);
}

.tech-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.1);
}

.tech-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  flex-shrink: 0;
}

.tech-content h4 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.tech-content p {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

/* 服务优势区域 */
.service-advantages-area {
  padding: 40px 30px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 20px 50px rgba(99, 102, 241, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  opacity: 0;
  transform: translateX(80px);
  transition: all 0.8s ease;
}

.service-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }

  .header-left {
    flex: none;
  }

  .products-title {
    font-size: 6rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
  }

  .advantages-layout {
    grid-template-columns: 1fr;
    gap: 50px;
  }

  .tech-advantages-area,
  .service-advantages-area {
    padding: 35px 25px;
  }
}

@media (max-width: 768px) {
  .products-hero {
    padding: 60px 0 40px;
  }

  .header-content {
    gap: 30px;
  }

  .products-title {
    font-size: 4rem;
  }

  .solutions-info {
    padding: 35px;
    backdrop-filter: blur(25px);
  }

  .solutions-title {
    font-size: 1.5rem;
  }

  .products-showcase {
    padding: 60px 0;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .advantages-section {
    padding: 60px 2%;
  }

  .advantages-section .section-title {
    font-size: 2rem;
    margin-bottom: 40px;
  }

  .advantages-layout {
    gap: 40px;
  }

  .tech-advantages-area,
  .service-advantages-area {
    padding: 30px 20px;
  }

  .tech-item {
    padding: 15px;
  }

  .service-card {
    padding: 25px;
  }
}

@media (max-width: 480px) {
  .header-content {
    gap: 20px;
  }

  .products-title {
    font-size: 3rem;
  }

  .solutions-info {
    padding: 30px;
    border-radius: 20px;
    backdrop-filter: blur(20px);
  }

  .product-content {
    padding: 25px;
  }

  .advantages-layout {
    gap: 30px;
  }

  .tech-advantages-area,
  .service-advantages-area {
    padding: 25px 15px;
  }

  .tech-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .tech-content h4 {
    font-size: 1.1rem;
  }

  .service-card {
    padding: 20px;
  }

  .service-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .service-title {
    font-size: 1.1rem;
  }
}

/* 服务卡片 */
.service-card {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 30px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 15px 40px rgba(99, 102, 241, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(50px);
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow:
    0 20px 50px rgba(99, 102, 241, 0.1),
    0 0 30px rgba(99, 102, 241, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.95);
  border-color: rgba(99, 102, 241, 0.2);
}

.service-card:hover::before {
  opacity: 1;
}

/* 服务头部 */
.service-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.service-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  color: white;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  flex-shrink: 0;
}

.service-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.3;
}

/* 服务内容 */
.service-content {
  margin-left: 0;
  margin-top: 15px;
}

.service-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #64748b;
  margin: 0 0 20px 0;
}

/* 服务特性点 */
.service-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
}

.feature-point {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #475569;
}

.feature-point i {
  color: #6366f1;
  font-size: 0.8rem;
  width: 14px;
  height: 14px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-point span {
  font-weight: 500;
}



/* ================================
   产品详情页面样式
   ================================ */

/* 产品详情主容器 */
.product-detail-main {
  padding-top: 80px;
  /* 导航栏高度 */
}

/* 面包屑导航 */
.breadcrumb {
  padding: 20px 0;
  background: #f8fafc;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
}

.breadcrumb-nav a {
  color: #6366f1;
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-nav a:hover {
  color: #4f46e5;
}

.breadcrumb-nav i {
  color: #94a3b8;
  font-size: 0.8rem;
}

.breadcrumb-nav span {
  color: #64748b;
  font-weight: 500;
}

/* 产品详情头部 */
.product-detail-hero {
  padding: 60px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
  position: relative;
  overflow: hidden;
}

.product-detail-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse 50% 30% at 20% 80%, rgba(99, 102, 241, 0.06) 0%, transparent 50%);
  pointer-events: none;
}

.product-detail-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  position: relative;
  z-index: 2;
}

.product-category {
  color: #6366f1;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}

.product-detail-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: #1e293b;
  margin: 0 0 20px 0;
  line-height: 1.2;
}

.product-detail-subtitle {
  font-size: 1.3rem;
  color: #64748b;
  margin: 0 0 25px 0;
  font-weight: 500;
}

.product-detail-description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #475569;
  margin: 0 0 30px 0;
}

/* 产品统计数据 */
.product-stats {
  display: flex;
  gap: 30px;
  margin: 30px 0;
  padding: 25px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.product-stats .stat-item {
  text-align: center;
}

.product-stats .stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: #6366f1;
  line-height: 1;
  margin-bottom: 5px;
}

.product-stats .stat-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

/* 产品操作按钮 */
.product-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.product-detail-image {
  position: relative;
}

.detail-img {
  width: 100%;
  height: auto;
  border-radius: 15px;
  box-shadow:
    0 20px 50px rgba(99, 102, 241, 0.15),
    0 0 40px rgba(99, 102, 241, 0.1);
}

/* 产品特性区域 */
.product-features-section {
  padding: 80px 0;
  background: white;
}

.product-features-section .section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 60px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-item {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 10px 30px rgba(99, 102, 241, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow:
    0 20px 40px rgba(99, 102, 241, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.95);
}

/* ================================
   联系我们页面样式
   ================================ */

/* 联系我们页面主容器 */
.contact-main {
  margin: 0;
  padding: 0;
}

/* 第一屏：公司大楼展示 */
.contact-hero-screen {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.contact-hero-screen .hero-background {
  width: 100%;
  height: 100%;
  background-image: url(company_3.jpg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

.contact-hero-screen .hero-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%);
  pointer-events: none;
}

.contact-hero-screen .hero-content {
  position: absolute !important;
  bottom: 80px;
  left: 80px;
  color: white;
  z-index: 2;
  width: auto !important;
  height: auto !important;
}

.contact-hero-screen .hero-title {
  font-size: 2.5rem;
  font-weight: 400;
  margin: 0 0 10px 0;
  line-height: 1.2;
}

.contact-hero-screen .hero-subtitle {
  font-size: 4rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.1;
  letter-spacing: 2px;
}

/* 第二屏：地图与地址区域 */
.address-map-section {
  min-height: 100vh;
  background: #f8fafc;
  padding: 80px 0;
}

.address-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: start;
}

.map-side {
  position: relative;
}

.map-frame {
  width: 100%;
  height: 500px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.map-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}

.map-placeholder i {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.9;
}

.map-placeholder p {
  font-size: 1.4rem;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.map-placeholder span {
  font-size: 1rem;
  opacity: 0.8;
}

.address-side {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.address-title {
  font-size: 3rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  font-family: 'Inter', sans-serif;
}

.address-divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  margin: 0;
}

/* 地址信息布局 */
.address-item {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.address-number {
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
  font-family: 'Inter', sans-serif;
  min-width: 60px;
}

.address-content {
  flex: 1;
}

.address-content .company-name {
  font-size: 1.4rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.address-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.address-text,
.contact-text {
  font-size: 1.1rem;
  color: #475569;
  margin: 0;
  line-height: 1.5;
}

.contact-text {
  font-weight: 600;
  color: #667eea;
}

/* 二维码区域 */
.contact-qrcode-section {
  margin-top: 30px;
}

.qrcode-container {
  text-align: center;
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  max-width: 220px;
}

.qrcode-placeholder {
  width: 160px;
  height: 160px;
  margin: 0 auto 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.footer-qrcode-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.qrcode-label {
  font-size: 1rem;
  color: #64748b;
  font-weight: 500;
}



/* 联系我们页面响应式 */
@media (max-width: 1200px) {
  .contact-hero-screen .hero-content {
    bottom: 60px;
    left: 60px;
  }

  .contact-hero-screen .hero-title {
    font-size: 2.2rem;
  }

  .contact-hero-screen .hero-subtitle {
    font-size: 3.5rem;
  }

  .address-container {
    gap: 60px;
    padding: 0 30px;
  }

  .address-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 992px) {
  .contact-hero-screen .hero-content {
    bottom: 50px;
    left: 50px;
  }

  .contact-hero-screen .hero-title {
    font-size: 2rem;
  }

  .contact-hero-screen .hero-subtitle {
    font-size: 3rem;
  }

  .address-container {
    grid-template-columns: 1fr;
    gap: 50px;
  }

  .map-frame {
    height: 400px;
  }

  .qrcode-placeholder {
    width: 140px;
    height: 140px;
  }
}

@media (max-width: 768px) {
  .contact-hero-screen .hero-content {
    bottom: 40px;
    left: 40px;
  }

  .contact-hero-screen .hero-title {
    font-size: 1.8rem;
  }

  .contact-hero-screen .hero-subtitle {
    font-size: 2.5rem;
  }

  .address-map-section {
    padding: 60px 0;
  }

  .address-container {
    padding: 0 20px;
    gap: 40px;
  }

  .address-title {
    font-size: 2.2rem;
  }

  .address-item {
    gap: 16px;
  }

  .address-number {
    font-size: 1.5rem;
    min-width: 40px;
  }

  .qrcode-placeholder {
    width: 140px;
    height: 140px;
  }
}

@media (max-width: 480px) {
  .contact-hero-screen .hero-content {
    bottom: 30px;
    left: 30px;
  }

  .contact-hero-screen .hero-title {
    font-size: 1.5rem;
  }

  .contact-hero-screen .hero-subtitle {
    font-size: 2rem;
  }

  .address-title {
    font-size: 1.8rem;
  }

  .map-frame {
    height: 300px;
  }

  .qrcode-placeholder {
    width: 120px;
    height: 120px;
  }


}

/* ================================
   第四屏：联系方式
   ================================ */

.contact-section {
  min-height: 100vh;
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.contact-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('assets/first_page/contacts_us.png') center/cover no-repeat;
  background-attachment: fixed;
}

.contact-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.contact-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100vh;
  display: flex;
  padding: 60px max(4%, 30px);
  box-sizing: border-box;
}

/* 左上方标题文字 */
.contact-message {
  flex: 1;
  display: flex;
  align-items: flex-start;
  padding-top: 3%;
}

.contact-title {
  font-size: 4.2rem;
  font-weight: 700;
  color: white;
  line-height: 1.5;
  /* 增加行高避免重叠 */
  margin: 0;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.title-line {
  display: block;
  margin-bottom: 15px;
  /* 增加行间距 */
}

/* 合作伙伴特效 */
.partner-text {
  display: inline-block;
  animation: partnerGlow 3s ease-in-out infinite;
}

@keyframes partnerGlow {

  0%,
  100% {
    color: white;
    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  }

  50% {
    color: #3b82f6;
    text-shadow:
      0 4px 12px rgba(0, 0, 0, 0.5),
      0 0 20px rgba(59, 130, 246, 0.6),
      0 0 30px rgba(59, 130, 246, 0.4);
  }
}

/* 右下角联系信息 */
.contact-info {
  position: absolute;
  bottom: 60px;
  right: max(3%, 30px);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 30px;
  min-width: 280px;
}

.contact-info-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin: 0 0 15px 0;
  text-align: center;
}

.contact-divider {
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.3);
  margin-bottom: 20px;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.contact-person {
  display: flex;
  flex-direction: column;
  gap: 8px;
  text-align: center;
}

.person-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
}

.person-phone {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'Courier New', monospace;
}

.contact-qrcode {
  text-align: center;
}

.qrcode-img {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}



/* ================================
   页脚
   ================================ */

.footer {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  padding: 60px 0 20px;
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3b82f6, #8b5cf6, #3b82f6, transparent);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
}

.footer-brand {
  flex: 1;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.company-logo-footer {
  height: 60px;
  width: auto;
  max-width: 200px;
}

.footer-logo i {
  font-size: 2rem;
  color: #3b82f6;
}

.footer-logo span {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
}

.footer-slogan {
  font-size: 1.3rem;
  color: white;
  margin: 15px 0 10px 0;
  font-weight: 600;
}

.footer-mission {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 10px 0;
  font-weight: 400;
  line-height: 1.6;
}

.footer-address {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.5;
}

.footer-bottom-link {
  color: rgba(255, 255, 255, 0.6) !important;
}

.footer-social {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.social-qrcodes {
  display: flex;
  gap: 30px;
}

.qrcode-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.qrcode-placeholder {
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
  padding: 4px;
}

.footer-qrcode-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.qrcode-placeholder:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  color: white;
  transform: translateY(-2px);
}

.qrcode-item span {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 20px;
  text-align: center;
}

.footer-bottom p {
  margin: 0;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.9rem;
}

/* 联系方式响应式设计 */
/* 超大屏幕进一步优化 */
@media (min-width: 1441px) {
  .contact-title {
    font-size: 4.0rem;
    /* 稍微减小字体避免过大 */
    line-height: 1.6;
    /* 更大的行高 */
  }

  .title-line {
    margin-bottom: 20px;
    /* 更大的行间距 */
  }
}

@media (max-width: 1200px) {
  .contact-title {
    font-size: 3.2rem;
    line-height: 1.5;
    /* 保持一致的行高 */
  }
}

/* 针对13.6寸屏幕及类似中等尺寸的优化 */
@media (max-width: 1440px) and (min-width: 1200px) {
  .contact-title {
    font-size: 3.6rem;
    line-height: 1.5;
    /* 统一使用1.5的行高 */
  }

  .title-line {
    margin-bottom: 18px;
    /* 增加行间距 */
  }
}

@media (max-width: 1360px) and (min-width: 1200px) {
  .contact-title {
    font-size: 3.4rem;
    line-height: 1.5;
    /* 保持一致的行高 */
  }

  .title-line {
    margin-bottom: 20px;
    /* 更大的行间距确保不重叠 */
  }
}

@media (max-width: 992px) {
  .contact-content {
    padding: 45px max(3.5%, 18px);
  }

  .contact-message {
    padding-top: 12%;
    padding-left: 15px;
    padding-right: 15px;
  }

  .contact-title {
    font-size: 2.8rem;
    margin: 0 auto;
  }

  .contact-info-section {
    bottom: 100px;
    /* 调整到更靠近底部 */
    right: auto !important;
    min-width: 350px;
  }

  .sliding-line {
    width: 55vw;
    transform: translateY(-90px);
  }

  .contact-slogan {
    font-size: 3.2rem;
    margin-top: -20px;
  }

  .contact-person-info {
    font-size: 1.6rem;
  }

  .qrcode-full {
    max-width: 130px;
  }
}

/* 中等屏幕优化（手机横屏/小平板） */
@media (max-width: 900px) and (min-width: 769px) {
  .contact-content {
    padding: 45px max(4%, 25px);
  }

  .contact-message {
    padding-top: 10%;
    margin-bottom: 40px;
  }

  .contact-title {
    font-size: 2.8rem;
    line-height: 1.4;
  }

  .contact-info-section {
    bottom: 80px;
    /* 调整位置避免重叠 */
    min-width: 320px;
  }

  .sliding-line {
    width: 50vw;
    transform: translateY(-60px);
  }

  .contact-slogan {
    font-size: 2.6rem;
  }
}

@media (max-width: 768px) {
  .contact-content {
    flex-direction: column;
    padding: 40px max(4%, 20px);
    min-height: 100vh;
    /* 确保有足够的高度 */
    justify-content: space-between;
    /* 分散布局 */
  }

  .contact-message {
    padding-top: 8%;
    /* 减少顶部空间 */
    padding-left: 20px;
    padding-right: 20px;
    margin-bottom: 50px;
    /* 增加与下方内容的距离 */
  }

  .contact-title {
    font-size: 2.4rem;
    /* 稍微减小字体 */
    margin: 0 auto;
    text-align: left;
    line-height: 1.4;
    /* 调整行高 */
  }

  .contact-info-section {
    position: relative;
    bottom: auto;
    right: auto;
    margin-top: 60px;
    /* 增加与上方的距离 */
    margin-bottom: 40px;
    align-items: flex-start;
    min-width: auto;
    width: 100%;
    padding: 0 10px;
    /* 添加内边距 */
  }

  .sliding-line {
    width: 60vw;
    /* 减少宽度避免过长 */
    transform: translateY(-40px);
    /* 调整垂直偏移 */
  }

  .contact-details-new {
    align-items: flex-start;
    text-align: left;
  }

  .contact-slogan {
    font-size: 2.4rem;
    /* 减小字体避免重叠 */
    margin-top: -10px;
    /* 调整间距 */
  }

  .contact-person-info {
    font-size: 1.4rem;
    /* 稍微减小字体 */
  }

  .qrcode-full {
    max-width: 110px;
    /* 稍微减小二维码 */
  }



  .footer-content {
    flex-direction: column;
    gap: 30px;
    text-align: center;
  }

  .social-qrcodes {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .contact-title {
    font-size: 1.8rem;
    margin: 0 auto;
    padding: 0 15px;
    /* 减少内边距 */
    text-align: left;
    line-height: 1.3;
    /* 调整行高 */
  }

  .contact-message {
    padding-top: 10%;
    /* 减少顶部空间 */
    padding-left: 15px;
    padding-right: 15px;
    margin-bottom: 40px;
    /* 增加底部间距 */
  }

  .partner-text {
    display: block;
  }

  .contact-info-section {
    gap: 15px;
    margin-top: 50px;
    /* 增加与上方的距离 */
    padding: 0 15px;
    /* 添加内边距 */
  }

  .sliding-line {
    width: 50vw;
    /* 进一步减少宽度 */
    transform: translateY(-30px);
    /* 调整垂直偏移 */
  }

  .contact-slogan {
    font-size: 2.0rem;
    /* 进一步减小字体 */
    margin-top: -8px;
    /* 调整间距 */
  }

  .contact-person-info {
    font-size: 1.3rem;
  }

  .qrcode-full {
    max-width: 100px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .contact-content {
    padding: 30px 10px;
  }

  .contact-title {
    font-size: 1.6rem;
    padding: 0 10px;
  }

  .contact-message {
    padding-top: 8%;
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 35px;
  }

  .contact-info-section {
    margin-top: 40px;
    padding: 0 10px;
  }

  .contact-slogan {
    font-size: 1.8rem;
  }

  .sliding-line {
    width: 45vw;
    transform: translateY(-25px);
  }
}

/* 右下角联系信息区域 */
.contact-info-section {
  position: absolute;
  bottom: 120px;
  /* 调整到更靠近底部 */
  right: auto !important;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 25px;
  min-width: 400px;
}

/* 滑动白线 */
.sliding-line {
  width: 60vw;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  border-radius: 1px;
  transform: translateY(-90px);
}

.sliding-line::before {
  content: '';
  position: absolute;
  top: 0;
  right: 100%;
  width: 30%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.8) 50%,
      transparent 100%);
  animation: slideRightToLeft 3s ease-in-out infinite;
}

@keyframes slideRightToLeft {
  0% {
    right: 100%;
  }

  50% {
    right: -30%;
  }

  100% {
    right: 100%;
  }
}

/* 联系信息内容 */
.contact-details-new {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 15px;
  text-align: left;
}

.contact-slogan {
  font-size: 3.8rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
  margin-top: -25px;
}

.contact-person-info {
  font-size: 1.8rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  font-family: 'Courier New', monospace;
}

.contact-qrcode-new {
  margin-top: 10px;
}

.qrcode-full {
  width: auto;
  height: auto;
  max-width: 150px;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 6px;
}

.qrcode-full:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.5);
}

/* 页尾升级样式 */
.qrcode-placeholder {
  width: 160px !important;
  height: 160px !important;
  padding: 6px;
}

.footer-qrcode-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.social-qrcodes {
  display: flex;
  gap: 30px;
  justify-content: center;
}

/* 二维码点击放大功能 */
.qrcode-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.qrcode-modal.show {
  opacity: 1;
  visibility: visible;
}

.qrcode-modal-content {
  position: relative;
  max-width: 400px;
  max-height: 400px;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.qrcode-modal.show .qrcode-modal-content {
  transform: scale(1);
}

.qrcode-modal-img {
  width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.qrcode-close {
  position: absolute;
  top: -15px;
  right: -15px;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: #333;
  transition: all 0.2s ease;
}

.qrcode-close:hover {
  background: white;
  transform: scale(1.1);
}

/* 可点击的二维码样式 */
.qrcode-full,
.footer-qrcode-img {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.qrcode-full:hover,
.footer-qrcode-img:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* 屏幕切换平滑过渡效果 */
.solutions-section,
.timeline-section,
.contact-section {
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 内容上滑动画 */
.solutions-container,
.timeline-container {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 初始状态由JavaScript控制 */
.solutions-container.animate-in,
.timeline-container.animate-in {
  transform: translateY(0) !important;
  opacity: 1 !important;
}

/* 整体滚动容器优化 - 整屏切换 */
html {
  scroll-behavior: smooth;
  /* scroll-snap-type由JavaScript动态控制 */
}

/* 所有主要section启用scroll-snap */
.hero-video-section,
.solutions-section,
.timeline-section,
.contact-section {
  scroll-snap-align: start;
  scroll-snap-stop: always;
}

/* 页脚也启用scroll-snap，确保从联系方式能够滚动进入 */
.footer {
  scroll-snap-align: start;
}

/* 响应式调整 */
@media (max-width: 768px) {


  .qrcode-modal-content {
    max-width: 300px;
    max-height: 300px;
    margin: 20px;
  }
}

/* 页脚二维码平铺显示 */
.qrcode-display-flat {
  display: flex;
  gap: 40px;
  align-items: center;
  justify-content: center;
}

.qrcode-item-flat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.qrcode-item-flat span {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  font-weight: 500;
}

/* 页脚二维码切换功能 */
.qrcode-switch-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.qrcode-tabs {
  display: flex;
  gap: 15px;
  background: rgba(255, 255, 255, 0.1);
  padding: 5px;
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.qrcode-tab {
  padding: 8px 20px;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.qrcode-tab:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.qrcode-tab.active {
  background: rgba(59, 130, 246, 0.8);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.qrcode-display {
  position: relative;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-display .qrcode-item {
  position: absolute;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.4s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.qrcode-display .qrcode-item.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.qrcode-display .qrcode-item span {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  font-weight: 500;
}

/* 升级页尾样式 */

/* ================================
   全面响应式设计优化
   ================================ */

/* 导航栏移动端优化 */
@media (max-width: 768px) {
  .nav-container {
    padding: 0;
    height: 75px;
  }

  .logo-image {
    width: 90px;
    height: 90px;
  }

  .nav-menu {
    position: fixed;
    top: 75px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 75px);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(30px);
    flex-direction: column;
    padding: 2rem 0;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-item {
    margin: 0.5rem 0;
    width: 100%;
    text-align: center;
  }

  .nav-link {
    font-size: 1rem !important;
    /* 强制统一字体大小 */
    padding: 1.2rem 2rem;
    display: block;
    border-radius: 10px;
    margin: 0 1.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
  }

  .nav-link:hover {
    background: rgba(0, 102, 255, 0.1);
    color: var(--primary-blue);
    transform: translateY(-2px);
  }

  .nav-toggle {
    display: flex;
    flex-direction: column;
    cursor: pointer;
    z-index: 1001;
    gap: 5px;
  }

  .nav-toggle span {
    width: 28px;
    height: 3px;
    background: var(--primary-blue);
    transition: all 0.3s ease;
    border-radius: 2px;
  }

  .nav-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(7px, 7px);
  }

  .nav-toggle.active span:nth-child(2) {
    opacity: 0;
  }

  .nav-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
  }

  .dropdown-menu {
    position: static;
    background: rgba(248, 250, 252, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: none;
    border: none;
    padding: 0.5rem 0;
    margin: 0.5rem 1.5rem;
    border-radius: 8px;
  }

  .dropdown-item {
    padding: 0.8rem 1.5rem;
    margin: 0.25rem 0;
    background: transparent;
    border-radius: 6px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
  }

  .dropdown-item:hover {
    background: rgba(0, 102, 255, 0.05);
  }
}

/* ================================
   公司介绍页面响应式
   ================================ */

/* ================================
   公司页面样式
   ================================ */

.company-main {
  padding: 0;
}

/* 第一屏：公司信息 */
.company-info-screen {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  background: #f8fafc;
  margin: 0;
  padding: 0;
}

.info-background {
  width: 100%;
  height: 100%;
  position: relative;
}

.info-background::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  background-image: url(company_2.png);
  background-size: cover;
  background-position: right bottom;
  background-repeat: no-repeat;
  opacity: 1;
  z-index: 1;
}

.info-content {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 80px;
  align-items: center;
  min-height: 80vh;
  position: relative;
  z-index: 2;
}

.info-left {
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-text {
  text-align: left;
}

.brand-line-1,
.brand-line-2 {
  font-size: 5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 0.9;
  letter-spacing: 3px;
}

.brand-line-3 {
  font-size: 3rem;
  font-weight: 600;
  color: #3b82f6;
  margin: 20px 0 0 0;
  line-height: 1;
}

.info-right {
  padding: 40px 0;
}

.company-name {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 30px 0;
  line-height: 1.2;
}

.company-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #475569;
}

.company-description p {
  margin: 0 0 20px 0;
}

.company-description p:last-child {
  margin-bottom: 0;
}

.highlight-text {
  color: #3b82f6;
  font-weight: 600;
}

/* 公司页面响应式 */
@media (max-width: 1200px) {
  .company-hero-screen .hero-content {
    bottom: 60px;
    left: 60px;
  }

  .company-hero-screen .hero-title {
    font-size: 2.2rem;
  }

  .company-hero-screen .hero-subtitle {
    font-size: 3.5rem;
  }

  .info-content {
    gap: 60px;
  }

  .brand-line-1,
  .brand-line-2 {
    font-size: 4.5rem;
  }

  .brand-line-3 {
    font-size: 2.5rem;
  }

  .company-name {
    font-size: 2.2rem;
  }
}

@media (max-width: 992px) {
  .company-hero-screen .hero-content {
    bottom: 50px;
    left: 50px;
  }

  .company-hero-screen .hero-title {
    font-size: 2rem;
  }

  .company-hero-screen .hero-subtitle {
    font-size: 3rem;
  }

  .info-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
    min-height: auto;
    padding: 60px 0;
  }

  .brand-text {
    text-align: center;
  }

  .brand-line-1,
  .brand-line-2 {
    font-size: 4rem;
  }

  .brand-line-3 {
    font-size: 2.2rem;
  }

  .company-name {
    font-size: 2rem;
  }

  .company-description {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .company-hero-screen .hero-content {
    bottom: 40px;
    left: 40px;
  }

  .company-hero-screen .hero-title {
    font-size: 1.8rem;
  }

  .company-hero-screen .hero-subtitle {
    font-size: 2.5rem;
    letter-spacing: 1px;
  }

  .info-content {
    gap: 30px;
    padding: 50px 0;
  }

  .brand-line-1,
  .brand-line-2 {
    font-size: 3rem;
    letter-spacing: 2px;
  }

  .brand-line-3 {
    font-size: 1.8rem;
    margin-top: 15px;
  }

  .company-name {
    font-size: 1.8rem;
    margin-bottom: 25px;
  }

  .company-description {
    font-size: 0.95rem;
    line-height: 1.7;
  }

  .company-description p {
    margin-bottom: 18px;
  }
}

@media (max-width: 480px) {
  .company-hero-screen .hero-content {
    bottom: 30px;
    left: 30px;
    right: 30px;
  }

  .company-hero-screen .hero-title {
    font-size: 1.5rem;
  }

  .company-hero-screen .hero-subtitle {
    font-size: 2rem;
    letter-spacing: 1px;
  }

  .info-content {
    gap: 25px;
    padding: 40px 0;
  }

  .brand-line-1,
  .brand-line-2 {
    font-size: 2.5rem;
    letter-spacing: 1px;
  }

  .brand-line-3 {
    font-size: 1.5rem;
    margin-top: 10px;
  }

  .company-name {
    font-size: 1.5rem;
    margin-bottom: 20px;
  }

  .company-description {
    font-size: 0.9rem;
    line-height: 1.6;
  }

  .company-description p {
    margin-bottom: 15px;
  }
}

/* ================================
   第三屏：核心团队介绍
   ================================ */

.team-screen {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  padding: 80px 0;
  margin: 0;
  z-index: 1;
}

.team-background {
  width: 100%;
  height: 100%;
  min-height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 30%, #f1f5f9 70%, #e8f4fd 100%);
  overflow: hidden;
}

/* 动态背景球 */
.floating-ball {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(139, 92, 246, 0.25), rgba(168, 85, 247, 0.2));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  opacity: 0.6;
  animation-duration: 15s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.1);
}

.ball-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-name: floatBall1;
  animation-delay: 0s;
}

.ball-2 {
  width: 150px;
  height: 150px;
  top: 20%;
  right: 15%;
  animation-name: floatBall2;
  animation-delay: 3s;
}

.ball-3 {
  width: 180px;
  height: 180px;
  bottom: 15%;
  left: 20%;
  animation-name: floatBall3;
  animation-delay: 6s;
}

.ball-4 {
  width: 120px;
  height: 120px;
  bottom: 25%;
  right: 10%;
  animation-name: floatBall4;
  animation-delay: 9s;
}

@keyframes floatBall1 {

  0%,
  100% {
    transform: translateY(0px) translateX(0px) scale(1);
  }

  25% {
    transform: translateY(-20px) translateX(10px) scale(1.1);
  }

  50% {
    transform: translateY(-10px) translateX(-15px) scale(0.9);
  }

  75% {
    transform: translateY(15px) translateX(5px) scale(1.05);
  }
}

@keyframes floatBall2 {

  0%,
  100% {
    transform: translateY(0px) translateX(0px) scale(1);
  }

  25% {
    transform: translateY(15px) translateX(-10px) scale(0.95);
  }

  50% {
    transform: translateY(-20px) translateX(12px) scale(1.08);
  }

  75% {
    transform: translateY(8px) translateX(-8px) scale(0.92);
  }
}

@keyframes floatBall3 {

  0%,
  100% {
    transform: translateY(0px) translateX(0px) scale(1);
  }

  25% {
    transform: translateY(-12px) translateX(15px) scale(1.06);
  }

  50% {
    transform: translateY(18px) translateX(-10px) scale(0.88);
  }

  75% {
    transform: translateY(-8px) translateX(8px) scale(1.02);
  }
}

@keyframes floatBall4 {

  0%,
  100% {
    transform: translateY(0px) translateX(0px) scale(1);
  }

  25% {
    transform: translateY(10px) translateX(-12px) scale(0.94);
  }

  50% {
    transform: translateY(-15px) translateX(8px) scale(1.12);
  }

  75% {
    transform: translateY(12px) translateX(-5px) scale(0.96);
  }
}

/* 团队容器 */
.team-screen .container {
  position: relative;
  z-index: 2;
  max-width: 100% !important;
  margin: 0 auto;
  padding: 0 max(1%, 10px) !important;
}

/* 团队标题 */
.team-header {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}

.team-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 15px 0;
  line-height: 1.2;
}

.team-subtitle {
  font-size: 1.2rem;
  color: #6366f1;
  margin: 0;
  font-weight: 500;
  letter-spacing: 2px;
  text-transform: uppercase;
}

/* 横向滚动团队轮播 */
.team-carousel-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  margin-bottom: 40px;
  padding: 0;
  margin-left: 0;
}

.team-carousel {
  position: relative;
  display: flex;
  align-items: flex-start;
  transition: transform 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  padding-left: 0;
  /* 宽度由JavaScript动态设置 */
}

/* 团队成员横向布局 */
.team-member {
  flex: 0 0 auto;
  width: auto;
  min-width: 280px;
  margin: 0 15px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  height: fit-content;
  box-sizing: border-box;
}



.team-member:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.member-avatar {
  margin-bottom: 25px;
}

.avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
}

.member-info {
  flex: 1;
  width: 100%;
  min-width: 0;
}

.member-name {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 10px 0;
  line-height: 1.2;
}

.member-position {
  font-size: 1.1rem;
  color: #6366f1;
  font-weight: 600;
  margin: 0 0 20px 0;
  line-height: 1.3;
}

.member-description {
  font-size: 0.95rem;
  color: #64748b;
  line-height: 1.6;
  text-align: left;
  margin: 0;
  word-wrap: break-word;
  hyphens: auto;
  flex: 1;
  width: 100%;
  max-width: 100%;
  overflow-wrap: break-word;
}



/* 团队页面响应式 */
/* 大屏幕：最大宽度500px，间距50px */
/* 大屏幕优化 */
@media (min-width: 1400px) {
  .team-screen .container {
    max-width: 100% !important;
    padding: 0 max(0.5%, 8px) !important;
  }

  .team-member {
    min-width: 320px;
  }
}

/* 超大屏幕优化 */
@media (min-width: 1600px) {
  .team-screen .container {
    max-width: 100% !important;
    padding: 0 max(0.3%, 5px) !important;
  }

  .team-member {
    min-width: 350px;
  }
}

/* 极大屏幕优化 */
@media (min-width: 1920px) {
  .team-screen .container {
    max-width: 100% !important;
    padding: 0 max(0.2%, 3px) !important;
  }

  .team-member {
    min-width: 380px;
  }
}

/* 中大屏幕：最大宽度450px，间距40px */
@media (min-width: 1200px) and (max-width: 1399px) {
  .team-member {
    min-width: 300px;
  }
}

/* 中等屏幕：最大宽度400px，间距35px */
@media (max-width: 1199px) and (min-width: 993px) {
  .team-title {
    font-size: 3rem;
  }

  .team-member {
    min-width: 280px;
    max-width: 400px;
    padding: 25px;
    width: fit-content;
  }

  .ball-1,
  .ball-2,
  .ball-3,
  .ball-4 {
    opacity: 0.4;
  }
}

/* 平板屏幕：宽度350px，间距30px */
@media (max-width: 992px) {
  .team-screen {
    padding: 60px 0;
  }

  .team-title {
    font-size: 2.8rem;
  }

  .team-subtitle {
    font-size: 1.1rem;
  }

  .team-member {
    min-width: 260px;
    max-width: 350px;
  }

  .team-carousel-container {
    min-height: 500px;
  }

  .team-header {
    margin-bottom: 40px;
  }
}

/* 小屏幕：宽度300px，间距25px */
@media (max-width: 768px) {
  .team-screen {
    padding: 50px 0;
  }

  .team-title {
    font-size: 2.4rem;
  }

  .team-subtitle {
    font-size: 1rem;
    letter-spacing: 1px;
  }

  .team-member {
    min-width: 240px;
    max-width: 300px;
    padding: 20px;
  }

  .member-name {
    font-size: 1.6rem;
  }

  .member-position {
    font-size: 1rem;
  }

  .member-description {
    font-size: 0.9rem;
  }

  .team-carousel-container {
    min-height: 400px;
  }

  .team-header {
    margin-bottom: 30px;
  }

  .floating-ball {
    opacity: 0.3;
  }

  .ball-1 {
    width: 120px;
    height: 120px;
  }

  .ball-2 {
    width: 100px;
    height: 100px;
  }

  .ball-3 {
    width: 110px;
    height: 110px;
  }

  .ball-4 {
    width: 80px;
    height: 80px;
  }
}

@media (max-width: 480px) {
  .team-screen {
    padding: 40px 0;
  }

  .team-title {
    font-size: 2rem;
  }

  .team-subtitle {
    font-size: 0.9rem;
  }

  .team-member {
    min-width: 220px;
    max-width: 280px;
    padding: 18px;
  }

  .member-name {
    font-size: 1.4rem;
  }

  .member-position {
    font-size: 0.95rem;
  }

  .member-description {
    font-size: 0.85rem;
    line-height: 1.5;
  }

  .avatar-placeholder {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .team-carousel-container {
    min-height: 350px;
  }

  .team-header {
    margin-bottom: 25px;
  }
}

/* ================================
   第四屏：社会责任
   ================================ */

/* 我们的责任屏幕 - 固定背景图片，文字居中 */
.responsibility-screen {
  height: 100vh;
  width: 100vw;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  overflow: hidden;
}

.responsibility-background {
  width: 100%;
  height: 100%;
  min-height: 100vh;
  position: fixed;
  /* 固定背景 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('assets/responsibility.jpg');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  z-index: -1;
  /* 确保背景在底层 */
}

.responsibility-overlay {
  position: fixed;
  /* 固定遮罩层 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 0;
}

.responsibility-screen .container {
  position: relative;
  z-index: 10;
  /* 确保内容在最顶层 */
  text-align: center;
  max-width: 1000px;
  padding: 0 40px;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.responsibility-content {
  color: white;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  /* 增加上下内边距 */
}

.responsibility-title {
  font-size: 4rem;
  font-weight: 700;
  color: white;
  margin: 0 0 50px 0;
  line-height: 1.2;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.responsibility-text {
  max-width: 700px;
  margin: 0 auto;
}

.responsibility-paragraph {
  font-size: 1.3rem;
  line-height: 1.8;
  color: white;
  margin: 0 0 30px 0;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.5);
  font-weight: 400;
}

.responsibility-paragraph:last-child {
  margin-bottom: 0;
}

/* 社会责任页面响应式 */
@media (max-width: 1200px) {
  .responsibility-screen {
    height: 100vh;
    min-height: 800px;
  }

  .responsibility-title {
    font-size: 3.5rem;
  }

  .responsibility-paragraph {
    font-size: 1.2rem;
  }

  .responsibility-screen .container {
    padding: 0 30px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .responsibility-background {
    background-attachment: scroll;
    /* 移动端改为滚动背景 */
  }
}

@media (max-width: 992px) {
  .responsibility-screen {
    height: 100vh;
    min-height: 700px;
  }

  .responsibility-title {
    font-size: 3rem;
    margin-bottom: 40px;
  }

  .responsibility-paragraph {
    font-size: 1.15rem;
    line-height: 1.7;
  }

  .responsibility-screen .container {
    padding: 0 25px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .responsibility-background {
    background-attachment: scroll;
    /* 移动端改为滚动背景 */
    position: absolute;
    /* 小屏幕改为绝对定位 */
  }

  .responsibility-overlay {
    position: absolute;
    /* 小屏幕改为绝对定位 */
  }
}

@media (max-width: 768px) {
  .responsibility-screen {
    height: 100vh;
    min-height: 600px;
  }

  .responsibility-title {
    font-size: 2.5rem;
    margin-bottom: 35px;
  }

  .responsibility-paragraph {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 25px;
  }

  .responsibility-screen .container {
    padding: 0 20px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .responsibility-content {
    max-width: 100%;
  }

  .responsibility-background {
    background-attachment: scroll;
    position: absolute;
  }

  .responsibility-overlay {
    position: absolute;
  }
}

@media (max-width: 480px) {
  .responsibility-screen {
    height: 100vh;
    min-height: 500px;
  }

  .responsibility-title {
    font-size: 2rem;
    margin-bottom: 30px;
  }

  .responsibility-paragraph {
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 20px;
  }

  .responsibility-screen .container {
    padding: 0 15px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .responsibility-content {
    padding: 30px 0;
    /* 调整小屏幕内边距 */
  }

  .responsibility-background {
    background-attachment: scroll;
    position: absolute;
  }

  .responsibility-overlay {
    position: absolute;
  }
}

/* ================================
   新闻详情页面样式
   ================================ */

.news-detail-main {
  padding-top: 80px;
}

.news-detail-hero {
  padding: 60px 0 40px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.news-detail-header {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.news-detail-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 30px 0;
  line-height: 1.3;
}

.news-detail-meta {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 0.95rem;
}

.meta-item i {
  color: #3b82f6;
}

.news-detail-content {
  padding: 60px 0;
  background: #ffffff;
}

.news-article {
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.8;
  color: #374151;
}

.article-image-hero {
  margin-bottom: 40px;
  text-align: center;
}

.article-image-hero img {
  width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.image-caption {
  margin-top: 12px;
  font-size: 0.9rem;
  color: #6b7280;
  font-style: italic;
}

.article-summary {
  background: #f8fafc;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 40px;
  border-left: 4px solid #3b82f6;
}

.article-summary p {
  font-size: 1.2rem;
  font-weight: 500;
  color: #1e293b;
  margin: 0;
  line-height: 1.6;
}

.article-section {
  margin-bottom: 50px;
}

.section-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 30px 0;
  border-bottom: 3px solid #3b82f6;
  padding-bottom: 12px;
}

.content-with-image {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
  align-items: start;
  margin-bottom: 30px;
}

.content-with-image.reverse {
  grid-template-columns: 300px 1fr;
}

.text-content p {
  margin-bottom: 20px;
  font-size: 1.1rem;
}

.image-content img {
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.highlight-box {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  padding: 25px;
  border-radius: 12px;
  margin: 30px 0;
  border-left: 4px solid #3b82f6;
}

.highlight-box h3 {
  color: #1e40af;
  margin: 0 0 15px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.highlight-box p {
  margin: 0;
  color: #1e293b;
  font-size: 1.05rem;
}

.image-full-width {
  margin: 40px 0;
  text-align: center;
}

.image-full-width img {
  width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.value-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin: 40px 0;
}

.value-item {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  text-align: center;
  border: 1px solid #e5e7eb;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.value-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px auto;
}

.value-icon i {
  font-size: 1.5rem;
  color: white;
}

.value-item h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 15px 0;
}

.value-item p {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

.case-study {
  background: #f0fdf4;
  padding: 25px;
  border-radius: 12px;
  margin-top: 25px;
  border-left: 4px solid #10b981;
}

.case-study h4 {
  color: #059669;
  margin: 0 0 15px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.case-study p {
  margin: 0;
  color: #1e293b;
  font-size: 1.05rem;
}

.image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin: 40px 0;
}

.gallery-item {
  text-align: center;
}

.gallery-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.tech-features {
  background: #fef3c7;
  padding: 25px;
  border-radius: 12px;
  margin-top: 25px;
  border-left: 4px solid #f59e0b;
}

.tech-features h4 {
  color: #d97706;
  margin: 0 0 15px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.tech-features ul {
  margin: 0;
  padding-left: 20px;
  color: #1e293b;
}

.tech-features li {
  margin-bottom: 10px;
  font-size: 1.05rem;
}

.cooperation-goals {
  background: #f3e8ff;
  padding: 25px;
  border-radius: 12px;
  margin-top: 25px;
  border-left: 4px solid #8b5cf6;
}

.cooperation-goals h4 {
  color: #7c3aed;
  margin: 0 0 15px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.cooperation-goals p {
  margin: 0;
  color: #1e293b;
  font-size: 1.05rem;
}

.article-tags {
  margin: 50px 0 40px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.tag {
  background: #3b82f6;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  transition: background 0.3s ease;
}

.tag:hover {
  background: #1d4ed8;
}

.article-share {
  border-top: 1px solid #e5e7eb;
  padding-top: 30px;
  margin-top: 40px;
}

.article-share h4 {
  margin: 0 0 20px 0;
  color: #1e293b;
  font-size: 1.2rem;
}

.share-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.share-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #374151;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.share-btn:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.related-news {
  margin-top: 60px;
  padding-top: 40px;
  border-top: 2px solid #e5e7eb;
}

.related-news h3 {
  font-size: 1.6rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 30px 0;
}

.related-items {
  display: grid;
  gap: 20px;
}

.related-item {
  display: flex;
  gap: 20px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.related-item:hover {
  background: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.related-item img {
  width: 100px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  flex-shrink: 0;
}

.related-content h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.related-content time {
  font-size: 0.9rem;
  color: #6b7280;
}

/* 新闻详情页面响应式 */
@media (max-width: 1024px) {

  .content-with-image,
  .content-with-image.reverse {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .value-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .news-detail-title {
    font-size: 2.2rem;
    padding: 0 20px;
  }

  .news-detail-meta {
    gap: 20px;
    padding: 0 20px;
  }

  .news-article {
    padding: 0 20px;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .text-content p {
    font-size: 1rem;
  }

  .value-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .image-gallery {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .share-buttons {
    justify-content: center;
  }

  .related-item {
    flex-direction: column;
    text-align: center;
  }

  .related-item img {
    width: 100%;
    height: 150px;
  }
}

@media (max-width: 480px) {
  .news-detail-title {
    font-size: 1.8rem;
  }

  .news-detail-meta {
    flex-direction: column;
    gap: 15px;
  }

  .article-tags {
    justify-content: center;
  }

  .share-buttons {
    flex-direction: column;
  }
}

/* ================================
   新闻页面样式
   ================================ */

.news-main {
  padding-top: 80px;
}

/* 新闻中心头部 */
.news-hero {
  padding: 80px 0 60px;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

/* 浮动的毛玻璃球 - 新闻页面版本 */
.news-hero::after {
  content: '';
  position: absolute;
  top: 15%;
  left: 8%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.12) 0%, rgba(139, 92, 246, 0.08) 50%, transparent 70%);
  backdrop-filter: blur(20px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: floatGlassBall1 15s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.news-hero .glass-ball-2 {
  position: absolute;
  top: 55%;
  right: 12%;
  width: 220px;
  height: 220px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.11) 0%, rgba(59, 130, 246, 0.07) 50%, transparent 70%);
  backdrop-filter: blur(18px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.25);
  animation: floatGlassBall2 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.news-hero .glass-ball-3 {
  position: absolute;
  top: 25%;
  right: 8%;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.09) 0%, rgba(99, 102, 241, 0.06) 50%, transparent 70%);
  backdrop-filter: blur(15px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  animation: floatGlassBall3 18s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.news-hero .glass-ball-4 {
  position: absolute;
  top: 70%;
  left: 45%;
  width: 190px;
  height: 190px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.08) 0%, rgba(99, 102, 241, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(12px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.18);
  animation: floatGlassBall4 22s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.news-hero .glass-ball-5 {
  position: absolute;
  top: 80%;
  left: 5%;
  width: 170px;
  height: 170px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.06) 50%, transparent 70%);
  backdrop-filter: blur(14px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.22);
  animation: floatGlassBall5 25s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.news-hero .glass-ball-6 {
  position: absolute;
  top: 20%;
  left: 65%;
  width: 130px;
  height: 130px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.08) 0%, rgba(99, 102, 241, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.15);
  animation: floatGlassBall6 19s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.news-header {
  position: relative;
  z-index: 2;
}

.news-hero .header-content {
  display: flex;
  align-items: flex-start;
  gap: 80px;
  width: 100%;
}

.news-hero .header-left {
  flex: 0 0 auto;
  min-width: 350px;
}

.news-hero .header-right {
  flex: 1;
  max-width: calc(100% - 430px);
  margin-left: 120px;
}

.news-hero .news-title {
  font-size: 8rem;
  font-weight: 800;
  color: #2d3748;
  margin: 0;
  line-height: 0.9;
  letter-spacing: -0.03em;
  font-family: 'Inter', sans-serif;
}

.news-info {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  backdrop-filter: none;
  box-shadow: none;
  position: relative;
  overflow: visible;
}

.news-info-title {
  font-size: 2.4rem;
  font-weight: 700;
  color: #6366f1;
  margin: 0 0 20px 0;
}

.news-description {
  font-size: 1.3rem;
  line-height: 1.8;
  color: #64748b;
  margin: 0;
  position: relative;
  z-index: 2;
}

.news-list {
  padding: 40px 0 80px 0;
  background: #f8fafc;
}

.news-list-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 50px;
}

.news-item {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.news-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.news-link {
  display: flex;
  text-decoration: none;
  color: inherit;
  height: 100%;
  align-items: center;
}

.news-image {
  flex: 0 0 350px;
  height: 200px;
  overflow: hidden;
}

.news-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-item:hover .news-img {
  transform: scale(1.05);
}

.news-content {
  flex: 1;
  padding: 30px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.news-content .news-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  font-size: 0.85rem;
  color: #6b7280;
}

.news-meta i {
  color: #3b82f6;
}

.news-divider {
  color: #d1d5db;
}

.news-summary {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 40px;
}

.page-btn {
  padding: 12px 20px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.page-btn:hover:not(:disabled) {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.page-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .news-hero .header-content {
    gap: 60px;
  }

  .news-hero .header-left {
    min-width: 300px;
  }

  .news-hero .header-right {
    margin-left: 80px;
  }

  .news-hero .news-title {
    font-size: 6rem;
  }

  .news-info-title {
    font-size: 2rem;
  }

  .news-description {
    font-size: 1.1rem;
  }
}

@media (max-width: 992px) {
  .news-image {
    flex: 0 0 300px;
    height: 180px;
  }

  .news-content {
    padding: 25px;
  }

  .news-content .news-title {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .news-hero {
    padding: 60px 0 40px;
  }

  .news-hero .header-content {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }

  .news-hero .header-left {
    min-width: auto;
  }

  .news-hero .header-right {
    margin-left: 0;
    max-width: 100%;
  }

  .news-hero .news-title {
    font-size: 4rem;
  }

  .news-info-title {
    font-size: 1.8rem;
  }

  .news-description {
    font-size: 1rem;
  }

  .news-link {
    flex-direction: column;
  }

  .news-image {
    flex: none;
    width: 100%;
    height: 220px;
  }

  .news-content {
    padding: 20px;
  }

  .news-content .news-title {
    font-size: 1.1rem;
  }

  .pagination {
    flex-wrap: wrap;
    gap: 8px;
  }

  .page-btn {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .news-hero {
    padding: 40px 0 30px;
  }

  .news-hero .header-content {
    gap: 30px;
  }

  .news-hero .news-title {
    font-size: 3rem;
  }

  .news-info-title {
    font-size: 1.5rem;
  }

  .news-description {
    font-size: 0.9rem;
  }

  .news-content {
    padding: 16px;
  }

  .news-content .news-title {
    font-size: 1rem;
  }

  .news-summary {
    font-size: 0.85rem;
  }

  .news-meta {
    font-size: 0.8rem;
  }
}



/* ================================
   场景页面响应式
   ================================ */

.scenario-detail-main {
  padding-top: 80px;
}

.scenario-detail-hero {
  padding: 60px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.scenario-detail-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.scenario-category {
  color: #3b82f6;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}

.scenario-detail-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 20px 0;
  line-height: 1.2;
}

.scenario-detail-description {
  font-size: 1.2rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

/* 场景页面响应式 */
@media (max-width: 1200px) {
  .scenario-detail-title {
    font-size: 3rem;
  }
}

@media (max-width: 992px) {
  .scenario-detail-title {
    font-size: 2.8rem;
    padding: 0 20px;
  }

  .scenario-detail-description {
    font-size: 1.1rem;
    padding: 0 20px;
  }
}

@media (max-width: 768px) {
  .scenario-detail-title {
    font-size: 2.4rem;
    padding: 0 25px;
  }

  .scenario-detail-description {
    font-size: 1rem;
    padding: 0 25px;
  }

  .scenario-detail-hero {
    padding: 40px 0;
  }

  .scenario-category {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .scenario-detail-title {
    font-size: 2rem;
    padding: 0 30px;
  }

  .scenario-detail-description {
    font-size: 0.95rem;
    padding: 0 30px;
  }

  .scenario-category {
    font-size: 0.8rem;
  }
}



/* ================================
   通用响应式优化
   ================================ */

/* 通用section间距 */
@media (max-width: 768px) {
  section {
    padding: 50px 0;
  }
}

@media (max-width: 480px) {
  section {
    padding: 40px 0;
  }
}

/* 通用容器间距 */
@media (max-width: 768px) {
  .container {
    padding: 0 max(4%, 20px);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 max(5%, 25px);
  }
}

/* 通用标题响应式 */
@media (max-width: 768px) {
  h1 {
    font-size: 2.4rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.6rem;
  }

  h4 {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.7rem;
  }

  h3 {
    font-size: 1.4rem;
  }

  h4 {
    font-size: 1.2rem;
  }
}

/* 通用按钮响应式 */
@media (max-width: 768px) {
  .btn {
    padding: 12px 20px;
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .btn {
    padding: 10px 18px;
    font-size: 0.9rem;
  }
}

/* 通用图片响应式 */
@media (max-width: 768px) {
  img {
    max-width: 100%;
    height: auto;
  }
}

/* 确保所有卡片在小屏幕上都有合适的间距 */
@media (max-width: 768px) {

  .card,
  .news-item,
  .product-card,
  .service-card,
  .value-item,
  .member-card {
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {

  .card,
  .news-item,
  .product-card,
  .service-card,
  .value-item,
  .member-card {
    margin-bottom: 15px;
  }
}

/* ================================
   落地案例页面样式
   ================================ */

/* 案例页面主容器 */
.cases-main {
  padding-top: 80px;
  /* 导航栏高度 */
}

/* 案例页面头部 */
.cases-hero {
  padding: 80px 0 60px;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.cases-hero .container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
}

/* 浮动的毛玻璃球 - 限制在cases区域 */
.cases-hero::after {
  content: '';
  position: absolute;
  top: 15%;
  left: 8%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.12) 0%, rgba(139, 92, 246, 0.08) 50%, transparent 70%);
  backdrop-filter: blur(20px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: floatGlassBall1 15s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.cases-hero .glass-ball-2 {
  position: absolute;
  top: 55%;
  right: 12%;
  width: 220px;
  height: 220px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.11) 0%, rgba(59, 130, 246, 0.07) 50%, transparent 70%);
  backdrop-filter: blur(18px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.25);
  animation: floatGlassBall2 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.cases-hero .glass-ball-3 {
  position: absolute;
  top: 25%;
  right: 8%;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.09) 0%, rgba(99, 102, 241, 0.06) 50%, transparent 70%);
  backdrop-filter: blur(15px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  animation: floatGlassBall3 18s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.cases-hero .glass-ball-4 {
  position: absolute;
  top: 70%;
  left: 45%;
  width: 190px;
  height: 190px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.08) 0%, rgba(99, 102, 241, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(12px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.18);
  animation: floatGlassBall4 22s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.cases-hero .glass-ball-5 {
  position: absolute;
  top: 80%;
  left: 5%;
  width: 170px;
  height: 170px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.06) 50%, transparent 70%);
  backdrop-filter: blur(14px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.22);
  animation: floatGlassBall5 25s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.cases-hero .glass-ball-6 {
  position: absolute;
  top: 20%;
  left: 65%;
  width: 130px;
  height: 130px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.08) 0%, rgba(99, 102, 241, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.15);
  animation: floatGlassBall6 19s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.cases-header {
  position: relative;
  z-index: 2;
}

.cases-header .header-content {
  display: flex;
  align-items: flex-start;
  gap: 80px;
  width: 100%;
}

.cases-header .header-left {
  flex: 0 0 auto;
  min-width: 350px;
}

.cases-header .header-right {
  flex: 1;
  max-width: calc(100% - 30px);
  margin: 0;
}

.cases-title {
  font-size: 8rem;
  font-weight: 800;
  color: #2d3748;
  margin: 0;
  line-height: 0.9;
  letter-spacing: -0.03em;
  font-family: 'Inter', sans-serif;
}

.cases-subtitle {
  font-size: 2.4rem;
  font-weight: 700;
  color: #6366f1;
  margin: 0 0 20px 0;
}

.cases-info {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  backdrop-filter: none;
  box-shadow: none;
  position: relative;
  overflow: visible;
}

.cases-description {
  font-size: 1.3rem;
  line-height: 1.8;
  color: #64748b;
  margin: 0 0 30px 0;
  position: relative;
  z-index: 2;
}

.cases-stats {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-top: 40px;
}

.cases-stats .stat-item {
  text-align: center;
}

.cases-stats .stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #6366f1;
  line-height: 1;
  margin-bottom: 8px;
}

.cases-stats .stat-label {
  font-size: 1rem;
  color: #64748b;
  font-weight: 500;
}

/* 案例列表 */
.cases-list {
  padding: 80px 0;
  background: white;
}

.cases-list .container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
}

/* 案例网格布局 */
.cases-grid {
  display: grid;
  gap: 60px;
  grid-template-columns: 1fr;
}

/* 超宽屏幕可以考虑双列布局（可选） */
@media (min-width: 1800px) {
  .cases-grid {
    grid-template-columns: 1fr 1fr;
    gap: 120px;
  }
}

/* 大屏幕优化间距 */
@media (min-width: 1400px) and (max-width: 1799px) {
  .cases-grid {
    gap: 80px;
  }
}

/* 中等大屏幕优化间距 */
@media (min-width: 1200px) and (max-width: 1399px) {
  .cases-grid {
    gap: 70px;
  }
}

/* 标准屏幕保持默认间距 */
@media (min-width: 992px) and (max-width: 1199px) {
  .cases-grid {
    gap: 60px;
  }
}

/* 平板设备优化间距 */
@media (max-width: 991px) {
  .cases-grid {
    gap: 50px;
  }
}

/* 手机设备优化间距 */
@media (max-width: 768px) {
  .cases-grid {
    gap: 40px;
  }
}

.case-item {
  position: relative;
  height: 100%;
  /* 确保网格中的案例项目高度一致 */
}

.case-content {
  margin: 0 auto;
  padding: 40px;
  background: #ffffff;
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.3);
  transition: all 0.3s ease;
}

.case-content:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
  transform: translateY(-4px);
}

.case-header {
  text-align: left;
  margin-bottom: 40px;
}

.case-title-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.case-category {
  display: inline-block;
  font-size: 0.8rem;
  font-weight: 600;
  color: #ffffff;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  padding: 4px 12px;
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.2);
  flex-shrink: 0;
}

.case-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.4;
  flex: 1;
}

.case-row {
  margin-top: 32px;
}

.case-overview {
  margin-bottom: 40px;
}

.case-overview h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 15px 0;
}

.case-overview p {
  font-size: 1rem;
  line-height: 1.7;
  color: #475569;
  margin: 0;
}

.case-content-grid {
  display: flex;
  gap: 48px;
  align-items: stretch;
  margin-top: 32px;
}

.case-image {
  flex: 1;
  min-width: 300px;
  max-width: 500px;
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
}

.image-carousel {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.carousel-container {
  flex: 1;
  position: relative;
  width: 100%;
  min-height: 0;
}

.case-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 32px;
  min-width: 0;
}

.case-text {
  padding: 0;
}

.case-highlights,
.case-results {
  margin-bottom: 30px;
}

.case-highlights h3,
.case-results h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 15px 0;
}

.case-results p {
  font-size: 1rem;
  line-height: 1.7;
  color: #475569;
  margin: 0;
}

.highlight-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px 24px;
}

.highlight-list li {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.95rem;
  color: #475569;
}

.highlight-list i {
  color: #6366f1;
  font-size: 0.9rem;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.project-image {
  width: 100%;
  height: 280px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.case-content:hover .project-image {
  transform: scale(1.02);
}

/* 案例总结 */
.cases-summary {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
}

.cases-summary .container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
}

.summary-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.summary-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 25px 0;
}

.summary-content p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #475569;
  margin: 0 0 50px 0;
}

.summary-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-top: 50px;
}

.summary-features .feature-item {
  text-align: center;
  padding: 30px 20px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  backdrop-filter: blur(20px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.05);
  transition: all 0.3s ease;
}

.summary-features .feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(99, 102, 241, 0.1);
}

.summary-features .feature-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.summary-features .feature-item h3 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 10px 0;
}

.summary-features .feature-item p {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 32px;
    text-align: center;
  }

  .header-left {
    flex: none;
  }

  .cases-title {
    font-size: 4rem;
  }

  .case-content-grid {
    flex-direction: column;
    gap: 32px;
  }

  .case-image {
    flex: none;
    max-width: none;
    min-width: auto;
  }

  .carousel-container {
    height: 300px;
  }

  .highlight-list {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .case-content {
    padding: 32px;
  }

  .case-title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .cases-hero {
    padding: 60px 0 40px;
  }

  .header-content {
    gap: 24px;
  }

  .cases-title {
    font-size: 2.5rem;
  }

  .cases-info {
    padding: 24px;
    border-radius: 16px;
  }

  .cases-subtitle {
    font-size: 1.2rem;
  }

  .cases-list {
    padding: 60px 0;
  }

  .case-item {
    margin-bottom: 80px;
  }

  .case-content {
    padding: 24px;
    margin: 0 16px;
  }

  .case-title {
    font-size: 1.5rem;
  }

  .case-content-grid {
    flex-direction: column;
    gap: 24px;
  }

  .case-image {
    flex: none;
    max-width: none;
    min-width: auto;
  }

  .carousel-container {
    height: 250px;
  }

  .highlight-list {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .case-title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .header-content {
    gap: 20px;
  }

  .cases-title {
    font-size: 3rem;
  }

  .cases-info {
    padding: 20px;
    border-radius: 16px;
  }

  .case-content {
    padding: 20px;
    margin: 0 8px;
  }

  .case-title {
    font-size: 1.4rem;
  }

  .case-content-grid {
    flex-direction: column;
    gap: 20px;
  }

  .case-image {
    flex: none;
    max-width: none;
    min-width: auto;
  }

  .carousel-container {
    height: 200px;
  }

  .highlight-list {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .case-title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }
}

/* 图片轮播样式 */

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.carousel-slide.active {
  opacity: 1;
}

.carousel-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0;
}

/* 移除轮播控制按钮 */

.carousel-dots {
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 6px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: #ffffff;
  transform: scale(1.25);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.dot:hover {
  background: rgba(255, 255, 255, 0.8);
}

/* 轮播自动播放暂停 */
.image-carousel:hover .carousel-slide {
  animation-play-state: paused;
}

/* ================================
   应用场景页面样式
   ================================ */

/* 应用场景主容器 */
.scenarios-main {
  margin: 0;
  padding: 0;
}

/* 视频区域 */
.scenarios-video-section {
  height: 100vh;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.scenarios-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

/* 视频背景遮罩 - 5%透明度 */
.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.05);
  z-index: 2;
  pointer-events: none;
}

/* 场景标签 - 椭圆形布局容器 */
.scenario-tags {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 4;
}

.scenario-tag {
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
  padding: 0;
  transition: all 0.3s ease;
}

.tag-content {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(74, 144, 226, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 30px;
  padding: 8px 20px 8px 8px;
  min-width: 140px;
  box-shadow: 0 4px 20px rgba(74, 144, 226, 0.3);
  transition: all 0.3s ease;
}

.tag-circle {
  width: 40px;
  height: 40px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tag-circle i {
  font-size: 18px;
  color: #4A90E2;
}

.tag-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.scenario-tag:hover .tag-content {
  background: rgba(74, 144, 226, 1);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 30px rgba(74, 144, 226, 0.5);
}

.scenario-tag:hover .tag-circle {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* 页面标题 */
.scenarios-title {
  position: absolute;
  bottom: 80px;
  left: 80px;
  z-index: 3;
  color: white;
}

.scenarios-title h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin: 0 0 10px 0;
  line-height: 1.2;
}

.scenarios-title p {
  font-size: 1.3rem;
  margin: 0;
  opacity: 0.9;
}

/* 信息弹出框 */
.scenario-info-box {
  position: absolute;
  z-index: 5;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 380px;
  opacity: 0;
  scale: 0.9;
  display: none;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.info-box-content {
  padding: 0;
  width: 100%;
}

.info-box-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.info-box-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.info-box-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #64748b;
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.info-box-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #1e293b;
}

.info-box-body {
  padding: 24px 28px 28px;
}

.info-section {
  margin-bottom: 24px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-section h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #3b82f6;
  margin: 0 0 12px 0;
}

.info-section p {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #475569;
  margin: 0;
}

.info-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-section li {
  font-size: 0.9rem;
  color: #64748b;
  padding: 6px 0 6px 20px;
  position: relative;
  line-height: 1.4;
}

.info-section li::before {
  content: '•';
  color: #6366f1;
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 6px;
}

/* 应用场景页面响应式 */
@media (max-width: 1200px) {
  .tag-content {
    min-width: 130px;
    padding: 7px 18px 7px 7px;
  }

  .tag-circle {
    width: 38px;
    height: 38px;
  }

  .tag-circle i {
    font-size: 17px;
  }

  .scenarios-title {
    bottom: 60px;
    left: 60px;
  }

  .scenarios-title h1 {
    font-size: 3rem;
  }

  .scenario-info-box {
    width: 350px;
  }
}

@media (max-width: 992px) {
  .tag-content {
    min-width: 120px;
    padding: 6px 16px 6px 6px;
  }

  .tag-circle {
    width: 35px;
    height: 35px;
  }

  .tag-circle i {
    font-size: 16px;
  }

  .tag-text {
    font-size: 13px;
  }

  .scenarios-title {
    bottom: 50px;
    left: 50px;
  }

  .scenarios-title h1 {
    font-size: 2.5rem;
  }

  .scenarios-title p {
    font-size: 1.1rem;
  }

  .scenario-info-box {
    width: 320px;
  }
}

@media (max-width: 768px) {
  .tag-content {
    min-width: 100px;
    padding: 5px 14px 5px 5px;
    gap: 8px;
  }

  .tag-circle {
    width: 30px;
    height: 30px;
  }

  .tag-circle i {
    font-size: 14px;
  }

  .tag-text {
    font-size: 12px;
  }

  .scenarios-title {
    bottom: 40px;
    left: 40px;
  }

  .scenarios-title h1 {
    font-size: 2rem;
  }

  .scenarios-title p {
    font-size: 1rem;
  }

  .scenario-info-box {
    position: fixed;
    top: 50%;
    left: 50%;
    right: auto;
    transform: translate(-50%, -50%);
    max-width: 90%;
    width: auto;
    min-width: 300px;
  }

  .info-box-header {
    padding: 20px 24px 16px;
  }

  .info-box-body {
    padding: 20px 24px 24px;
  }

  .info-section {
    margin-bottom: 20px;
  }
}

@media (max-width: 600px) {
  .tag-content {
    min-width: 90px;
    padding: 4px 12px 4px 4px;
    gap: 6px;
  }

  .tag-circle {
    width: 28px;
    height: 28px;
  }

  .tag-circle i {
    font-size: 12px;
  }

  .tag-text {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .scenarios-title {
    bottom: 30px;
    left: 30px;
  }

  .scenarios-title h1 {
    font-size: 1.8rem;
  }

  .scenarios-title p {
    font-size: 0.9rem;
  }

  .tag-content {
    min-width: 80px;
    padding: 4px 10px 4px 4px;
    gap: 6px;
  }

  .tag-circle {
    width: 26px;
    height: 26px;
  }

  .tag-circle i {
    font-size: 11px;
  }

  .tag-text {
    font-size: 10px;
  }

  .scenario-info-box {
    min-width: 280px;
    max-width: 85%;
  }

  .info-box-header {
    padding: 16px 20px 12px;
  }

  .info-box-header h3 {
    font-size: 1.3rem;
  }

  .info-box-body {
    padding: 16px 20px 20px;
  }
}

/* 响应式设计已在主样式中定义 */
