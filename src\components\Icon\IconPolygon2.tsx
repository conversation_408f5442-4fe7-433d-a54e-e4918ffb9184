/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 17:47:27
 * @LastEditTime: 2024-12-16 17:47:38
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}
export const IconPolygon2 = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="11"
      height="13"
      viewBox="0 0 11 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M11 6.5L0.499999 12.5622L0.5 0.437822L11 6.5Z" fill="#2B65F6" />
    </svg>
  );
};
