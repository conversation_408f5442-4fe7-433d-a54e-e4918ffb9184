// js/news-list.js
// 新闻数据配置
const newsData = [
  {
    title: "基孔肯雅热来势汹汹？刘起勇首席教你这样防，附科技硬招",
    link: "https://mp.weixin.qq.com/s/2yDDQbpS_oGWZGEaeDVygA",
    img: "news_center/news_list/14.jpg",
    alt: "基孔肯雅热来势汹汹？刘起勇首席教你这样防，附科技硬招",
    source: "星小尘资讯",
    date: "2025年7月",
    summary: "2025年7月29日，广东省疾控局通报显示，截至7月26日，全省累计报告基孔肯雅热 4824 例，均为轻症。病例分布在佛山2882例，广州22例，中山18例，东莞、珠海、河源各3例，江门、阳江、肇庆各2例，清远、深圳、湛江各1例。"
  },
  {
    title: "AI赋能蚊媒监测——银河星尘亮相2025全国卫生杀虫药械学术交流会",
    link: "https://mp.weixin.qq.com/s/cxTf5e-fs0g7fjPuOsm8dQ",
    img: "news_center/news_list/13.jpg",
    alt: "AI赋能蚊媒监测",
    source: "星小尘资讯",
    date: "2025年7月",
    summary: "2025年7月15日-16日，中华预防医学会主办的“全国卫生杀虫药械学术交流会”在重庆召开。在这场最高规格的年度聚会上，“AI赋能病媒监测”成为高频关键词，而银河星尘携全国首个“病媒AI大模型产品”应邀参会，用实力诠释了“国家所需，星尘所向”。"
  },
  {
    title: "AI赋能病媒防控：深圳各大公园迈入智能监测新时代",
    link: "https://mp.weixin.qq.com/s/Tk9SvNTNUa4sNIUY3zMZ5w",
    img: "news_center/news_list/12.jpg",
    alt: "AI赋能病媒防控",
    source: "星小尘资讯",
    date: "2025年7月",
    summary: "深圳银河星尘科技有限公司自主研发的病媒AI大模型监测系统，已成功落地中山公园、前海公园、光明欢乐田园、科苑公园等地。"
  },
  {
    title: "从「传统人工」到「AI 监测」：银河星尘病媒 AI 大模型监测系统及设备亮相中博会",
    link: "https://mp.weixin.qq.com/s/gUAp5x0eoe8I7iIc-Pm9FQ",
    img: "news_center/news_list/11.jpg",
    alt: "银河星尘亮相中博会",
    source: "星小尘资讯",
    date: "2025年7月",
    summary: "深圳银河星尘科技有限公司作为\"病媒 AI 大模型监测\"领域的新星，凭借其创新的病媒 AI 大模型监测平台，成为展会焦点，向业界展示了 AI 技术赋能病媒生物监测与防控中的强大潜力，成功吸引众多专业观众目光。"
  },
  {
    title: "中国疾控中心专家亲临银河星尘，共探AI赋能病媒监测新突破",
    link: "https://mp.weixin.qq.com/s/wc8vT4ureXI_bhZJy9QC2w",
    img: "news_center/news_list/10.jpg",
    alt: "中国疾控中心专家再次莅临",
    source: "星小尘资讯",
    date: "2025年6月",
    summary: "2025年6月24日至6月27日，中国疾病预防控制中心传染病预防控制所专家郭玉红再度莅临深圳银河星尘科技有限公司，开启为期四天的技术交流与经验共享。"
  },
  {
    title: "AI 赋能：银河星尘联合中建五局，推动病媒防控智能化变革",
    link: "https://mp.weixin.qq.com/s/wc8vT4ureXI_bhZJy9QC2w",
    img: "news_center/news_list/9.jpg",
    alt: "银河星尘联合中建五局智能化变革",
    source: "星小尘资讯",
    date: "2025年6月",
    summary: "近日，深圳银河星尘科技有限公司自主研发的病媒 AI 大模型监测系统成功应用于深圳湾超级总部基地的建设工程中"
  },
  {
    title: "银河星尘与华中科技大学展开人工智能在生态环境领域应用研讨",
    link: "https://mp.weixin.qq.com/s/VsfgpEoex-DgpqRZDZUBnQ",
    img: "news_center/news_list/7.jpg",
    alt: "与华中科技大学合作研讨",
    source: "星小尘资讯",
    date: "2025年6月",
    summary: "5月29日，深圳银河星尘科技有限公司与华中科技大学组织了一场关于人工智能技术赋能生态环境领域的专题研讨会，共同探索人工智能技术的创新应用与行业变革潜力"
  },
  {
    title: "团队Vlog｜船已靠岸，但我们的故事正驶向下一片海",
    link: "https://mp.weixin.qq.com/s/uu7URaPIbYDS9PR-vYTzmw",
    img: "news_center/news_list/6.jpg",
    alt: "团队Vlog帆船活动",
    source: "星小尘资讯",
    date: "2025年5月",
    summary: "忙碌的日子里也要记得给生活加点甜呀～放下键盘与数据来一场与自然相拥的约会！🌊帆船逐浪 🌾田园漫步 🍲农家烟火"
  },
  {
    title: "盛夏到来！耳边\"情话\"变蚊子的\"嗡嗡咒\"，这谁能顶得住？",
    link: "https://mp.weixin.qq.com/s/9EVOCyxpcndFLohIXiMABg",
    img: "news_center/news_list/5.jpg",
    alt: "盛夏蚊虫防控资讯",
    source: "星小尘资讯",
    date: "2025年5月",
    summary: "各位朋友请留意！眼下正值盛夏时节气温持续升高，雨水频繁蚊虫开始活跃，被叮咬后奇痒难耐、抓挠留疤甚至可能传播疾病……到底什么人会深得蚊子喜欢呢？"
  },
  {
    title: "国家CDC传染病所郭玉红专家亲临指导：AI病媒大模型平台获权威认可，共筑公共卫生防控新防线",
    link: "https://mp.weixin.qq.com/s?__biz=MzkzNzkzMzg2OA==&mid=2247483736&idx=1&sn=4c0c154057a728e55809b566fab22607&chksm=c315dfbd6d408272c40b9a1b2bd9d1d607b29dab177f5408c2955fd9007bae7a0e93824abbab&scene=126&sessionid=1752122965#rd",
    img: "news_center/news_list/4.jpg",
    alt: "国家CDC传染病所郭玉红专家指导",
    source: "星小尘资讯",
    date: "2025年4月",
    summary: "2025年4月27日至2025年4月30日，中国疾病预防控制中心传染病预防控制所（简称：传染病所）专家郭玉红莅临深圳银河星尘科技有限公司（简称：银河星尘），开启为期四天的技术交流与经验共享"
  },
  {
    title: "AI领航新赛道，银河星尘携手国健院掀起AI病媒融合创新新篇章",
    link: "https://mp.weixin.qq.com/s?__biz=MzkzNzkzMzg2OA==&mid=2247483676&idx=1&sn=0c7afb0c302f47ef5b934edaf98afb68&chksm=c33cecb43195cd75b25b2d02952528ed0944e69d437de4560fdc827319b03fa42dfb7f7fd21d&scene=126&sessionid=1752118240#rd",
    img: "news_center/news_list/3.jpg",
    alt: "2025AI病媒融合创新大会",
    source: "星小尘资讯",
    date: "2025年3月",
    summary: "2025年3月15日，由国家健康医疗大数据研究院（深圳）主办、深圳银河星尘科技有限公司承办的\"2025AI病媒融合创新大会\"在香港中文大学（深圳）盛大召开并圆满落幕"
  },
  {
    title: "银河星尘科技有限公司与深圳市疾控中心技术交流",
    link: "news-detail-shenzhen-cdc-december.html",
    img: "news_center/news_list/2.jpg",
    alt: "银河星尘与深圳市疾控中心技术交流",
    source: "星小尘资讯",
    date: "2024年12月",
    summary: "银河星尘科技有限公司与深圳市疾控中心于近日开展专项技术交流，围绕病媒生物监测技术创新与数据应用深化合作共识，共同推进智慧化公共卫生防控体系建设"
  },
  {
    title: "银河星尘携手国家 CDC 重点实验室，共探病媒生物防控新征程",
    link: "news-detail-cdc-november.html",
    img: "news_center/news_list/1.jpg",
    alt: "银河星尘与国家CDC重点实验室合作",
    source: "星小尘资讯",
    date: "2024年11月",
    summary: "近日，银河星尘科技有限公司与国家CDC传染病预防控制国家重点实验室达成深度合作，双方围绕病媒生物智能监测、疫情预警与防控决策等核心领域展开技术攻坚。此次合作标志着公司正式进入国家级公共卫生科研体系，为智慧化病媒生物防控注入创新动能"
  }
];

const pageSize = 5;
let currentPage = 1;
const totalPages = Math.ceil(newsData.length / pageSize);

function renderNewsList(page) {
  const container = document.getElementById('news-list-container');
  container.innerHTML = '';
  const start = (page - 1) * pageSize;
  const end = Math.min(start + pageSize, newsData.length);
  for (let i = start; i < end; i++) {
    const news = newsData[i];
    const article = document.createElement('article');
    article.className = 'news-item';
    article.innerHTML = `
      <a href="${news.link}" target="_blank" class="news-link">
        <div class="news-image">
          <img src="${news.img}" alt="${news.alt}" class="news-img">
        </div>
        <div class="news-content">
          <h3 class="news-title">${news.title}</h3>
          <div class="news-meta">
            <i class="fas fa-user-circle"></i>
            <span class="news-source">${news.source}</span>
            <span class="news-divider">|</span>
            <time class="news-date">${news.date}</time>
          </div>
          <p class="news-summary">${news.summary}</p>
        </div>
      </a>
    `;
    container.appendChild(article);
  }
}

function renderPagination() {
  const pagination = document.getElementById('pagination');
  pagination.innerHTML = '';

  // 上一页
  const prevBtn = document.createElement('button');
  prevBtn.className = 'page-btn';
  prevBtn.textContent = '上一页';
  prevBtn.disabled = currentPage === 1;
  prevBtn.onclick = () => changePage(currentPage - 1);
  pagination.appendChild(prevBtn);

  // 页码按钮
  for (let i = 1; i <= totalPages; i++) {
    const btn = document.createElement('button');
    btn.className = 'page-btn' + (i === currentPage ? ' active' : '');
    btn.textContent = i;
    btn.onclick = () => changePage(i);
    pagination.appendChild(btn);
  }

  // 下一页
  const nextBtn = document.createElement('button');
  nextBtn.className = 'page-btn';
  nextBtn.textContent = '下一页';
  nextBtn.disabled = currentPage === totalPages;
  nextBtn.onclick = () => changePage(currentPage + 1);
  pagination.appendChild(nextBtn);
}

function changePage(page) {
  if (page < 1 || page > totalPages) return;
  currentPage = page;
  renderNewsList(currentPage);
  renderPagination();
  window.scrollTo({ top: 0, behavior: 'smooth' });
}

// 页面整体滑动动画初始化
function pageAnimate() {
  // 创建Intersection Observer来监控元素进入视口
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        // 添加动画类
        entry.target.classList.add('animate-in');
      } else {
        // 元素离开视口时移除动画类（支持反向动画）
        entry.target.classList.remove('animate-in');
      }
    });
  }, {
    threshold: 0.1, // 当元素10%可见时触发
    rootMargin: '0px 0px -50px 0px' // 提前50px触发动画
  });

  // 为所有需要动画的元素添加观察
  const animatedElements = document.querySelectorAll('.page-section, .slide-in-left, .slide-in-right, .slide-in-scale');
  animatedElements.forEach(el => {
    observer.observe(el);
  });
}

document.addEventListener('DOMContentLoaded', function () {
  renderNewsList(currentPage);
  renderPagination();
  pageAnimate();
});
