/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 01:41:43
 * @LastEditTime: 2024-12-18 19:08:20
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  url: string;
  title: string;
  content: string | any;
  className?: string;
}

export const PicShow = (props: IProps) => {
  const { url, title, content, className } = props;

  const style = {
    bg:
      `flex flex-col items-center absolute left-0 right-0 -bottom-[1.2rem] font-normal ` +
      `text-[#C5C6CB] text-center leading-[.28rem] mt-[.4rem]`,
    gradient:
      'bg-gradient-to-b from-[#05030B]/0 from-[0%] via-[#05030B]/82 via-[49.12%]  to-[#05030B]/100 to-[89.47%] ',
  };

  return (
    <div className={`relative flex flex-col items-center ${className} `}>
      <div className={`relative rounded-[8px] w-[7.3rem] h-[4rem]`}>
        <div className={`absolute right-0 left-0 bottom-0 h-[100px]  ${style.gradient}`}></div>
        <img src={url} className="rounded-[8px] w-[7.3rem] h-[4rem]" />
      </div>
      <div className={style.bg}>
        <div className={`text-white text-[.24rem] font-semibold w-full text-center pb-[.4rem]`}>
          {title}
        </div>
        <div className="w-[3.06rem] font-normal text-[.16rem]">{content}</div>
      </div>
    </div>
  );
};
