/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 11:00:06
 * @LastEditTime: 2024-12-16 11:00:19
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}
export const IconImage = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="30"
      height="30"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.34012 25.9099L13.5858 17.6642C14.0808 17.1692 14.3283 16.9217 14.6137 16.8289C14.8648 16.7474 15.1352 16.7474 15.3863 16.8289C15.6717 16.9217 15.9192 17.1692 16.4142 17.6642L24.6049 25.8549M17.5 18.75L21.0858 15.1642C21.5808 14.6692 21.8283 14.4217 22.1137 14.3289C22.3648 14.2474 22.6352 14.2474 22.8863 14.3289C23.1717 14.4217 23.4192 14.6692 23.9142 15.1642L27.5 18.75M12.5 11.25C12.5 12.6307 11.3807 13.75 10 13.75C8.61929 13.75 7.5 12.6307 7.5 11.25C7.5 9.86929 8.61929 8.75 10 8.75C11.3807 8.75 12.5 9.86929 12.5 11.25ZM8.5 26.25H21.5C23.6002 26.25 24.6503 26.25 25.4525 25.8413C26.1581 25.4817 26.7317 24.9081 27.0913 24.2025C27.5 23.4003 27.5 22.3502 27.5 20.25V9.75C27.5 7.6498 27.5 6.5997 27.0913 5.79754C26.7317 5.09193 26.1581 4.51825 25.4525 4.15873C24.6503 3.75 23.6002 3.75 21.5 3.75H8.5C6.3998 3.75 5.3497 3.75 4.54754 4.15873C3.84193 4.51825 3.26825 5.09193 2.90873 5.79754C2.5 6.5997 2.5 7.6498 2.5 9.75V20.25C2.5 22.3502 2.5 23.4003 2.90873 24.2025C3.26825 24.9081 3.84193 25.4817 4.54754 25.8413C5.3497 26.25 6.3998 26.25 8.5 26.25Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
