# 病媒AI - 大模型监测系统官网

## 项目简介

这是一个专为病媒AI大模型监测系统设计的现代化企业官网，采用前沿的Web技术栈打造，具有强烈的科技感和优秀的用户体验。

## 🚀 核心特性

### 🎨 设计风格
- **明亮科技风**：参考OpenAI和科大讯飞的设计理念，采用明亮色系主导
- **3D立体效果**：大量使用3D变换和立体视觉效果
- **强交互性**：全站配备GSAP驱动的微交互动画
- **AI元素**：集成粒子动画、神经网络连接线、流体效果等AI视觉元素

### 🎭 动画效果
- **粒子背景系统**：动态粒子云背景，支持鼠标交互
- **3D轨道运动**：8个关键词卡片围绕中央科学家角色做轨道运动
- **神经网络可视化**：动态神经网络连接线动画
- **GSAP微交互**：悬浮反馈、视差滚动、平滑过渡等
- **页面入场动画**：分层次的页面元素入场效果

### 📱 响应式设计
- **完美适配**：桌面端、平板、手机全设备适配
- **触摸优化**：针对触摸设备的特殊交互优化
- **性能优化**：GPU硬件加速、动画节流、资源懒加载

## 🛠️ 技术栈

- **HTML5**: 语义化结构设计
- **CSS3**: 现代CSS特性，包含Grid、Flexbox、CSS变量等
- **JavaScript ES6+**: 现代JavaScript语法
- **GSAP**: 高性能动画库
- **Canvas API**: 粒子系统渲染
- **SVG**: 矢量图形和动画
- **Font Awesome**: 图标字体库

## 📁 项目结构

```
病媒AI官网/
├── index.html          # 主页面文件
├── styles.css          # 样式表文件
├── script.js           # 交互脚本文件
└── README.md           # 项目说明文档
```

## 🎯 页面功能

### 导航栏
- 响应式导航菜单
- 下拉菜单（应用场景、企业信息）
- 移动端汉堡菜单
- 滚动时导航栏背景变化

### 首页Banner
- **主标题**：病媒AI大模型监测系统
- **副标题**：让人工智能成为保护人类健康，增强公共卫生防疫能力的强大引擎
- **3D场景**：中央发光球体 + 同心圆环 + 光束旋转 + 浮动粒子
- **暗色科技风**：深蓝到黑色渐变背景，营造神秘科技感

### 交互特效
- **粒子系统**：50个动态粒子，支持鼠标跟随
- **中央发光球体**：多层渐变发光效果，支持悬浮和点击交互
- **同心圆环**：5层扩展动画，错时出现营造科技感
- **旋转光束**：4条不同颜色的光束围绕中心旋转
- **浮动粒子**：6个彩色粒子上下浮动
- **神经网络**：SVG绘制的动态连接线

## 🚀 快速开始

1. **下载项目文件**
   ```bash
   # 确保以下文件在同一目录下
   - index.html
   - styles.css  
   - script.js
   ```

2. **启动本地服务器**
   ```bash
   # 使用Python启动本地服务器
   python -m http.server 8000
   
   # 或使用Node.js的live-server
   npx live-server
   
   # 或直接用浏览器打开index.html
   ```

3. **访问网站**
   ```
   http://localhost:8000
   ```

## 🎨 颜色主题

### 主色调
```css
--primary-blue: #00d4ff      /* 主蓝色 */
--secondary-purple: #7b68ee  /* 辅助紫色 */
--accent-pink: #ff6b9d       /* 强调粉色 */
--tech-silver: #c0c7d1       /* 科技银色 */
```

### 背景渐变
```css
/* Hero区域暗色科技风 */
background: linear-gradient(135deg, 
    #0a0e1a 0%,      /* 深黑蓝 */
    #1a1f3a 30%,     /* 暗蓝 */
    #2a3284 60%,     /* 科技蓝 */
    #1a1f3a 100%     /* 暗蓝 */
);
```

## 📱 响应式断点

- **桌面端**: > 1200px
- **平板端**: 968px - 1200px  
- **移动端**: < 968px
- **小屏移动端**: < 576px

## ⚡ 性能优化

### 动画优化
- GPU硬件加速
- `transform` 和 `opacity` 优先使用
- 动画节流控制在60fps
- `will-change` 属性优化

### 加载优化
- CDN资源加载
- 图片懒加载
- 关键CSS内联
- JavaScript异步加载

## 🔧 自定义配置

### 修改粒子数量
```javascript
// 在script.js中修改
for (let i = 0; i < 50; i++) {  // 改变数字调整粒子数量
    particles.push(new Particle());
}
```

### 调整轨道速度
```css
/* 在styles.css中修改 */
.keyword-card {
    animation: orbit 20s linear infinite;  /* 改变20s调整速度 */
}
```

### 更换颜色主题
```css
/* 在styles.css的:root中修改 */
:root {
    --primary-blue: #your-color;
    --secondary-purple: #your-color;
    /* ... 其他颜色变量 */
}
```

## 📞 技术支持

如需技术支持或功能定制，请联系开发团队。

## 📄 许可证

本项目仅用于病媒AI官网展示，版权所有。

---

**病媒AI** - 让人工智能成为保护人类健康的强大引擎 