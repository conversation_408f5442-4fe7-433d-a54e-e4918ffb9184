import imgLogoNameWhite from '@/assets/img_logo_name_white.png';
import imgLogoWhite from '@/assets/logo-white.svg';
import { AIChatBox } from '@/components/AIChatBox';
import { history } from '@umijs/max';
import { Button } from 'antd';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-14 00:11:14
 * @LastEditTime: 2024-12-18 11:38:38
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export default function Xxc() {
  return (
    <div className="flex flex-row w-full h-full bg-[#17161B] p-[16px]">
      <div className="flex flex-col w-[230px] bg-[#202029] border border-[#E9E9E90D] p-[24px]">
        <header className="flex gap-[10px] items-center justify-center">
          <img src={imgLogoWhite} className="w-[36px] h-[32.73px]" />
          <img src={imgLogoNameWhite} className="w-[100px] h-[23.4px]" />
        </header>
        {/* 预留智能体 */}
        <section className="flex-1"></section>
        <footer className="flex flex-col h-[104px] justify-end">
          {/* 暂时隐藏 */}
          {/* <Button type="primary" className="w-[180px] h-[44px]">
            <span className='text-[14px] font-normal'>立即登录</span>
          </Button> */}
          <Button
            ghost
            className="w-[180px] h-[44px] !border-[#ECECEC1A]"
            onClick={() => {
              history.push('/home');
            }}
          >
            <span className="text-[#FFE8E8] text-[14px] font-normal">返回官网</span>
          </Button>
        </footer>
      </div>
      <div className="flex flex-col flex-1 items-center h-[calc(100vh-32px)]">
        <div className="relative flex w-[11rem] h-[calc(100vh-32px)]">
          <AIChatBox />
        </div>
      </div>
    </div>
  );
}
