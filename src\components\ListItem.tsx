import { IconEllipse } from '@/components/Icon/IconEllipse';
import { ReactNode } from 'react';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 13:36:37
 * @LastEditTime: 2024-12-18 16:18:59
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

interface IProps {
  title: string;
  content: string | ReactNode;
}

export const CustomerListItem = (props: IProps) => {
  const { title, content } = props;
  return (
    <div className="flex flex-col px-[.3rem] gap-[.10rem]">
      <div className="flex items-center gap-[.1rem]">
        <IconEllipse className="size-[.16rem]" />
        <span className="text-[.24rem] font-medium">{title}</span>
      </div>
      <div className="text-[.18rem] text-[#666666]">{content}</div>
    </div>
  );
};
