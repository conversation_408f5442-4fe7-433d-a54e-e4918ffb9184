/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-17 00:16:19
 * @LastEditTime: 2024-12-18 16:23:13
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}
export const IconPresentation = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.99967 10.6667V14M7.99967 10.6667L11.9997 14M7.99967 10.6667L3.99967 14M13.9997 2V7.46667C13.9997 8.58677 13.9997 9.14682 13.7817 9.57465C13.5899 9.95097 13.284 10.2569 12.9077 10.4487C12.4798 10.6667 11.9198 10.6667 10.7997 10.6667H5.19967C4.07957 10.6667 3.51952 10.6667 3.09169 10.4487C2.71537 10.2569 2.40941 9.95097 2.21766 9.57465C1.99967 9.14682 1.99967 8.58677 1.99967 7.46667V2M5.33301 6V8M7.99967 4.66667V8M10.6663 7.33333V8M14.6663 2H1.33301"
        stroke="#999999"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
