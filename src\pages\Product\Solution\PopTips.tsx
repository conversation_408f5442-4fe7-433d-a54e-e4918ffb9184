/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-15 22:40:24
 * @LastEditTime: 2024-12-15 22:57:41
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  tips: string;
  className?: string;
}

export const PopTips = (props: IProps) => {
  const { tips, className } = props;
  return (
    <div
      className={`rounded-[.8rem] w-[2.22rem] flex flex-row bg-white/[.22] backdrop-blur-[16px] items-center gap-[.23rem] p-[.1rem]  ${className}`}
    >
      <div className="flex flex-col w-[.56rem] h-[.56rem] rounded-[.28rem] bg-[#0067FF] text-[.18rem] font-semibold items-center justify-center text-white">
        AI
      </div>
      <span className="text-white text-[.18rem]">{tips}</span>
    </div>
  );
};
