/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-17 16:32:39
 * @LastEditTime: 2024-12-17 18:42:14
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}

export const IconDroplets = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_49_1721)">
        <path
          d="M14.6667 10.6667C14.6667 12.8758 12.8758 14.6667 10.6667 14.6667C8.45757 14.6667 6.66671 12.8758 6.66671 10.6667C6.66671 7.7909 10.6667 1.33337 10.6667 1.33337C10.6667 1.33337 14.6667 7.7909 14.6667 10.6667Z"
          stroke="black"
          strokeWidth="1.3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M5.33337 6.00004C5.33337 7.10461 4.43794 8.00004 3.33337 8.00004C2.2288 8.00004 1.33337 7.10461 1.33337 6.00004C1.33337 4.56214 3.33337 1.33337 3.33337 1.33337C3.33337 1.33337 5.33337 4.56214 5.33337 6.00004Z"
          stroke="black"
          strokeWidth="1.3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_49_1721">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
