// js/footer.js
// 动态渲染footer内容，替代iframe
function renderFooter() {
  const footerHtml = `
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-brand">
            <div class="footer-logo">
              <img src="company_logo.png" alt="银河星尘" class="company-logo-footer" />
            </div>
            <p class="footer-slogan">为实现病媒可控而创新</p>
            <p class="footer-mission">以人工智能为引擎，赋能公共卫生防疫体系，守护人类健康安全</p>
            <p class="footer-address">深圳市宝安区汇智研发中心B座3楼318</p>
          </div>
          <div class="footer-social">
            <div class="qrcode-display-flat">
              <div class="qrcode-item-flat">
                <div class="qrcode-placeholder">
                  <img src="assets/last_page/qrcode.jpg" alt="服务号二维码" class="footer-qrcode-img" />
                </div>
                <span>扫码关注服务号</span>
              </div>
              <div class="qrcode-item-flat">
                <div class="qrcode-placeholder">
                  <img src="assets/first_page/contacts_qrcode.png" alt="客服微信二维码" class="footer-qrcode-img" />
                </div>
                <span>扫码联系我们</span>
              </div>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>
            &copy;2024-2025 深圳银河星尘科技有限公司｜<a class="footer-bottom-link" href="https://beian.miit.gov.cn/" target="_blank">粤ICP备2024351884号-1</a>
          </p>
        </div>
      </div>
    </footer>
  `;
  let footerContainer = document.getElementById('footer-container');
  if (!footerContainer) {
    footerContainer = document.createElement('div');
    footerContainer.id = 'footer-container';
    document.body.appendChild(footerContainer);
  }
  footerContainer.innerHTML = footerHtml;
}
document.addEventListener('DOMContentLoaded', renderFooter);
