/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 13:04:17
 * @LastEditTime: 2024-12-17 12:54:32
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { commonStyles } from '@/constants/styles';
import { ReactNode } from 'react';

interface IProps {
  content: string | ReactNode;
}

export const PrimaryText = (props: IProps) => {
  const { content } = props;
  return <span className={commonStyles.colorPrimary}>{content}</span>;
};
