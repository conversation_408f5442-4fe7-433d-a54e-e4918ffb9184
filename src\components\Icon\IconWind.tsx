/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-17 16:31:39
 * @LastEditTime: 2024-12-18 16:12:56
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

interface IProps {
  className?: string;
}

export const IconWind = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14 12C14 12 13.2064 11.687 12.6667 11.5347C9.25314 10.5716 6.74686 13.4284 3.33333 12.4653C2.79361 12.3131 2 12 2 12M14 8.00004C14 8.00004 13.2064 7.68703 12.6667 7.53475C9.25314 6.57164 6.74686 9.42845 3.33333 8.46533C2.79361 8.31305 2 8.00004 2 8.00004M14 4.00004C14 4.00004 13.2064 3.68703 12.6667 3.53475C9.25314 2.57164 6.74686 5.42845 3.33333 4.46533C2.79361 4.31305 2 4.00004 2 4.00004"
        stroke="black"
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
