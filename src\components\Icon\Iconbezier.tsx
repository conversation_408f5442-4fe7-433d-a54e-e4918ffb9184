/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 11:00:41
 * @LastEditTime: 2024-12-16 11:03:29
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}

export const IconBezier = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="30"
      height="30"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.4997 25.3292C8.63987 24.3982 5.60163 21.3599 4.67077 17.4999M25.3294 17.5C24.3985 21.3599 21.3603 24.3981 17.5005 25.3291M17.5003 4.67072C21.3602 5.60161 24.3985 8.63991 25.3294 12.4999M4.67102 12.5C5.6019 8.63999 8.64025 5.60162 12.5003 4.67072M4.5 17.5H5.5C6.20007 17.5 6.5501 17.5 6.81749 17.3638C7.05269 17.2439 7.24392 17.0527 7.36376 16.8175C7.5 16.5501 7.5 16.2001 7.5 15.5V14.5C7.5 13.7999 7.5 13.4499 7.36376 13.1825C7.24392 12.9473 7.05269 12.7561 6.81749 12.6362C6.5501 12.5 6.20007 12.5 5.5 12.5H4.5C3.79993 12.5 3.4499 12.5 3.18251 12.6362C2.94731 12.7561 2.75608 12.9473 2.63624 13.1825C2.5 13.4499 2.5 13.7999 2.5 14.5V15.5C2.5 16.2001 2.5 16.5501 2.63624 16.8175C2.75608 17.0527 2.94731 17.2439 3.18251 17.3638C3.4499 17.5 3.79993 17.5 4.5 17.5ZM24.5 17.5H25.5C26.2001 17.5 26.5501 17.5 26.8175 17.3638C27.0527 17.2439 27.2439 17.0527 27.3638 16.8175C27.5 16.5501 27.5 16.2001 27.5 15.5V14.5C27.5 13.7999 27.5 13.4499 27.3638 13.1825C27.2439 12.9473 27.0527 12.7561 26.8175 12.6362C26.5501 12.5 26.2001 12.5 25.5 12.5H24.5C23.7999 12.5 23.4499 12.5 23.1825 12.6362C22.9473 12.7561 22.7561 12.9473 22.6362 13.1825C22.5 13.4499 22.5 13.7999 22.5 14.5V15.5C22.5 16.2001 22.5 16.5501 22.6362 16.8175C22.7561 17.0527 22.9473 17.2439 23.1825 17.3638C23.4499 17.5 23.7999 17.5 24.5 17.5ZM14.5 7.5H15.5C16.2001 7.5 16.5501 7.5 16.8175 7.36376C17.0527 7.24392 17.2439 7.05269 17.3638 6.81749C17.5 6.5501 17.5 6.20007 17.5 5.5V4.5C17.5 3.79993 17.5 3.4499 17.3638 3.18251C17.2439 2.94731 17.0527 2.75608 16.8175 2.63624C16.5501 2.5 16.2001 2.5 15.5 2.5H14.5C13.7999 2.5 13.4499 2.5 13.1825 2.63624C12.9473 2.75608 12.7561 2.94731 12.6362 3.18251C12.5 3.4499 12.5 3.79993 12.5 4.5V5.5C12.5 6.20007 12.5 6.5501 12.6362 6.81749C12.7561 7.05269 12.9473 7.24392 13.1825 7.36376C13.4499 7.5 13.7999 7.5 14.5 7.5ZM14.5 27.5H15.5C16.2001 27.5 16.5501 27.5 16.8175 27.3638C17.0527 27.2439 17.2439 27.0527 17.3638 26.8175C17.5 26.5501 17.5 26.2001 17.5 25.5V24.5C17.5 23.7999 17.5 23.4499 17.3638 23.1825C17.2439 22.9473 17.0527 22.7561 16.8175 22.6362C16.5501 22.5 16.2001 22.5 15.5 22.5H14.5C13.7999 22.5 13.4499 22.5 13.1825 22.6362C12.9473 22.7561 12.7561 22.9473 12.6362 23.1825C12.5 23.4499 12.5 23.7999 12.5 24.5V25.5C12.5 26.2001 12.5 26.5501 12.6362 26.8175C12.7561 27.0527 12.9473 27.2439 13.1825 27.3638C13.4499 27.5 13.7999 27.5 14.5 27.5Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
