import imgActivity1 from '@/assets/activity/img_activity_1.jpg';
import imgActivity1_1 from '@/assets/activity/img_activity_1_1.jpg';
import imgActivity1_2 from '@/assets/activity/img_activity_1_2.jpg';
import { ReactComponent as ArrowRight } from '@/assets/activity/arrow-right.svg';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Button, ConfigProvider } from 'antd';
import { history } from '@umijs/max';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-14 00:14:31
 * @LastEditTime: 2024-12-18 11:27:10
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export default function Activity() {
  const sectionStyle = 'mt-[.3rem] w-[15.2rem] mx-auto text-[.16rem] leading-[.32rem]';
  return (
    <div className="flex flex-col">
      <header
        className="relative h-[7.62rem]"
        style={{
          backgroundImage: `url(${imgActivity1})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
        }}
      >
        <div
          className="pt-[3.44rem] mx-auto text-white font-semibold text-[.66rem] text-center
        bg-[#0649BC6B] h-full"
        >
          领先的数字化病媒生物监测
          <br />
          与治理领域服务商
        </div>
      </header>
      {/* 跨区卡片 */}
      <div className="bg-[#EEF5FD]">
        <div
          className={`${sectionStyle} p-[.2rem_0_0.3rem_0] border-solid border-b-[1px] border-[#ccc] flex items-center cursor-pointer`}
        >
          <ArrowRight />
          <div
            onClick={() => {
              history.back();
            }}
            className="ml-[.08rem]"
          >
            返回列表
          </div>
        </div>
        <section className={sectionStyle}>
          <p>科技引领未来</p>
          <p className="">
            随着人工智能技术的飞速发展，银河星尘科技有限公司自豪地宣布，我们将举办一场前所未有的科技盛宴。在这里，您将见证AI大模型如何重塑我们的生活和工作方式。加入我们，一起探索机器学习、物联网设备和地理信息系统的融合创新，体验智能科技带来的无限可能。活动时间:
            2024年10月15日，地点:月之暗面科技总部。未来已来，你准备好了吗?
          </p>
          <img src={imgActivity1_1} className="w-full object-cover" />
        </section>
        <section className={sectionStyle}>
          <p>创新驱动发展一</p>
          <p>
            诚邀您参加我们的年度科技活动，共同见证病媒生物监测技术的革新。
            本次活动将展示我们如何利用先进的AI大模型、物联网设备和GIS技术，为用户提供全面、高效的病媒监测预警服务。
            这不仅是一场技术的展示，更是对未来健康监测趋势的深入探讨。
          </p>
          <p>
            活动时间:2024年10月20日，地点:月之暗面科技展示中心。智慧监测，守护健康，期待您的参与
          </p>
          <img src={imgActivity1_2} className=" w-full object-cover" />
          <div className="w-full h-[1rem] flex justify-end items-center pr-[2rem] gap-[.3rem]">
            <ConfigProvider
              theme={{
                components: {
                  Button: {
                    defaultBg: '#EEF5FD',
                  },
                },
              }}
            >
              <Button icon={<LeftOutlined />}>上一篇</Button>
            </ConfigProvider>
            <Button type="primary" icon={<RightOutlined />} iconPosition="end">
              下一篇
            </Button>
          </div>
        </section>
      </div>
    </div>
  );
}
