/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-17 16:32:10
 * @LastEditTime: 2024-12-18 16:13:22
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}
export const IconThermometer = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14 2.00004L10 2.00004M14 4.66671L10 4.66671M14 7.33337L10 7.33337M3.66671 9.17189V3.00004C3.66671 2.07957 4.4129 1.33337 5.33337 1.33337C6.25385 1.33337 7.00004 2.07957 7.00004 3.00004V9.17189C7.80403 9.71006 8.33337 10.6266 8.33337 11.6667C8.33337 13.3236 6.99023 14.6667 5.33337 14.6667C3.67652 14.6667 2.33337 13.3236 2.33337 11.6667C2.33337 10.6266 2.86272 9.71006 3.66671 9.17189ZM6.00004 11.6667C6.00004 12.0349 5.70156 12.3334 5.33337 12.3334C4.96518 12.3334 4.66671 12.0349 4.66671 11.6667C4.66671 11.2985 4.96518 11 5.33337 11C5.70156 11 6.00004 11.2985 6.00004 11.6667Z"
        stroke="black"
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
