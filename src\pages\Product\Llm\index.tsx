/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-14 00:11:14
 * @LastEditTime: 2024-12-18 18:19:51
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import imgIot3 from '@/assets/iot/img_iot_3.jpg';
import imgLLM1 from '@/assets/llm/img_llm_1.webp';
import imgLLM2 from '@/assets/llm/img_llm_2.jpg';
import imgLLM3 from '@/assets/llm/img_llm_3.jpg';
import { Circle } from '@/components/Circle';
import { IconBarChart2 } from '@/components/Icon/IconBarChart2';
import { IconBarLine } from '@/components/Icon/IconBarLine';
import { IconContainer } from '@/components/Icon/IconContainer';
import { IconData } from '@/components/Icon/IconData';
import { IconDataFlow } from '@/components/Icon/IconDataFlow';
import { IconMessage } from '@/components/Icon/IconMessage';
import { PrimaryText } from '@/components/PrimaryText';
import { SectionTitle } from '@/components/SectionTips';

export default function Llm() {
  return (
    <div className="flex flex-col w-full">
      <header
        className="h-[5.64rem] flex flex-col items-center justify-center gap-[.34rem]"
        style={{
          backgroundImage: `url(${imgLLM1})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
        }}
      >
        <div className="text-[.64rem] text-white font-semibold">
          <p>来自国内最权威专业机构</p>
        </div>
        <div
          className="flex flex-col justify-center items-center text-[.32rem] text-white rounded-[.6rem] w-[5.58rem] h-[.82rem]"
          style={{ background: 'linear-gradient(297.39deg, #3A60E7 17.76%, #894DE7 82.93%)' }}
        >
          亿级训练数据
        </div>
        <div className="text-white flex gap-[.11rem] text-[.16rem] ">
          <div className="flex items-center gap-[.1rem] rounded-[.6rem] bg-[#0067FF] px-[.2rem] h-[.44rem]">
            <IconMessage className="size-[.2rem]" />
            专业疑问答疑
          </div>
          <div className="flex items-center gap-[.1rem] rounded-[.6rem] bg-[#0067FF] px-[.2rem] h-[.44rem]">
            <IconBarChart2 className="size-[.2rem]" />
            病媒大数据分析
          </div>
          <div className="flex items-center gap-[.1rem] rounded-[.6rem] bg-[#0067FF] px-[.2rem] h-[.44rem]">
            <IconBarLine className="size-[.2rem]" />
            病媒智能预测
          </div>
        </div>
      </header>
      <section
        className="w-full h-[10.8rem]"
        style={{
          backgroundImage: `url(${imgIot3})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
        }}
      >
        <SectionTitle
          className="mt-[1.31rem] mb-[.62rem]"
          classNames={{
            content: 'text-[.18rem]',
            tips: 'text-[.18rem]',
          }}
          title="大模型介绍"
          content="星尘大模型结合国外实例(非洲已应用)，利用算法模型加新型卷积神经网络，可支持提升病媒种类精准度，结合物联网设备，应用端数据传输处理更快、更精准，且设备体积可控"
          tips="从蚊虫特征使用多模态加新型卷积神经网络模型进行预训练、微调"
        />
        <div className="w-full flex justify-center items-center">
          <img src={imgLLM2} className="w-[9.6rem] h-[5.2rem]" />
        </div>
      </section>
      <section className="w-full h-[9.4rem] bg-[#EEF5FD]">
        <SectionTitle
          className="mt-[1.31rem] mb-[.62rem]"
          classNames={{
            content: 'text-[.18rem]',
            tips: 'text-[.18rem]',
          }}
          iconColor="#0067FF"
          title={
            <span className="text-black">
              大模型<span className="text-[#0067FF]">数据</span>表现
            </span>
          }
        />
        <div className="flex flex-row items-center justify-center gap-[1.84rem] ">
          <div className="flex flex-col w-[6.8rem] h-[5.2rem] gap-[.3rem]">
            <div className="rounded-[.12rem] w-[6.8rem] h-[1.56rem] bg-white flex flex-row items-center px-[.3rem] gap-[.26rem]">
              <Circle className="flex items-center justify-center size-[.68rem] bg-[#F3F5F9]">
                <IconContainer className="w-[.24rem] h-[.24rem]" />
              </Circle>
              <div className="w-[4.78rem] text-[.18rem] text-[#666666]">
                <span>
                  模型在验证中的精度、召回率和平均精度(MAP) 分别为
                  <PrimaryText content="96.00%、" />
                  <PrimaryText content="90.50%" />
                  和 <PrimaryText content="95.87%" />
                </span>
              </div>
            </div>
            <div className="rounded-[.12rem] w-[6.8rem] h-[1.56rem] bg-white flex flex-row items-center px-[.3rem] gap-[.26rem]">
              <Circle className="flex items-center justify-center size-[.68rem] bg-[#F3F5F9]">
                <IconData className="w-[.24rem] h-[.24rem]" />
              </Circle>
              <div className="w-[4.78rem] text-[.18rem] text-[#666666]">
                <span>
                  种类分类模型的准确率为
                  <PrimaryText content="92.40±2%，" />
                  性别分类模型的准确率为
                  <PrimaryText content="97.00±1%" />
                </span>
              </div>
            </div>
            <div className="rounded-[.12rem] w-[6.8rem] h-[1.56rem] bg-white flex flex-row items-center px-[.3rem] gap-[.26rem]">
              <Circle className="flex items-center justify-center size-[.68rem] bg-[#F3F5F9]">
                <IconDataFlow className="w-[.24rem] h-[.24rem]" />
              </Circle>
              <div className="w-[4.78rem] text-[.18rem] text-[#666666]">
                <span>结果准确度高:蚊媒类别识别正确、蚊媒信息表述正确、蚊媒数量正确</span>
              </div>
            </div>
          </div>
          <div className="flex w-[6.28rem] h-[6.28rem]">
            <img src={imgLLM3} className="w-[6.28rem] h-[6.28rem]" />
          </div>
        </div>
      </section>
    </div>
  );
}
